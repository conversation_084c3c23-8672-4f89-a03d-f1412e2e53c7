// FANUC设备数据采集器模块 - 简化版
// 本模块实现FANUC CNC设备的数据采集功能，参考fanuc_v2的实现
package collect

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"net/http"
	"os"
	"sync"
	"time"

	"shared/logger"
)

// DeviceConfig 设备配置信息结构体
// 功能：存储FANUC设备的连接和采集配置信息
type DeviceConfig struct {
	ID              string `json:"id"`               // 设备唯一标识符
	Name            string `json:"name"`             // 设备名称
	IP              string `json:"ip"`               // 设备IP地址
	Port            int    `json:"port"`             // 设备端口号
	Brand           string `json:"brand"`            // 设备品牌
	Model           string `json:"model"`            // 设备型号
	Location        string `json:"location"`         // 设备位置
	DataType        string `json:"data_type"`        // 数据类型
	Enabled         bool   `json:"enabled"`          // 是否启用
	AutoStart       bool   `json:"auto_start"`       // 是否自动启动
	CollectInterval int    `json:"collect_interval"` // 数据采集间隔(毫秒)
	Timeout         int    `json:"timeout"`          // 连接超时时间(秒)
	RetryCount      int    `json:"retry_count"`      // 重试次数
	RetryDelay      int    `json:"retry_delay"`      // 重试延迟(秒)
}

// DeviceStatus 设备状态结构体
// 功能：记录设备的连接状态和运行统计信息
type DeviceStatus struct {
	Connected    bool      `json:"connected"`     // 设备是否已连接
	LastSeen     time.Time `json:"last_seen"`     // 最后一次成功通信时间
	ErrorCount   int       `json:"error_count"`   // 累计错误次数
	LastError    string    `json:"last_error"`    // 最后一次错误信息
	CollectCount int64     `json:"collect_count"` // 累计数据采集次数
	PushCount    int64     `json:"push_count"`    // 累计数据推送次数
}

// DeviceData 设备数据结构体
// 功能：存储从FANUC设备采集到的完整数据
type DeviceData struct {
	DeviceID   string                 `json:"device_id"`   // 设备ID
	DeviceName string                 `json:"device_name"` // 设备名称
	DataType   string                 `json:"data_type"`   // 数据类型
	Location   string                 `json:"location"`    // 设备位置
	Timestamp  time.Time              `json:"timestamp"`   // 数据采集时间戳
	Status     DeviceStatus           `json:"status"`      // 设备状态信息
	RawData    map[string]interface{} `json:"raw_data"`    // 原始采集数据
}

// PushResponse API推送响应结构体
// 功能：定义API推送接口的标准响应格式
type PushResponse struct {
	Success bool        `json:"success"` // 推送是否成功
	Message string      `json:"message"` // 响应消息
	Data    interface{} `json:"data"`    // 响应数据
}

// FanucCollector FANUC设备采集器结构体
// 功能：实现单个FANUC设备的数据采集和API推送功能
type FanucCollector struct {
	config     DeviceConfig       // 设备配置信息
	connected  bool               // 设备连接状态
	collecting bool               // 采集运行状态
	mu         sync.RWMutex       // 读写锁，保护共享状态
	status     DeviceStatus       // 设备状态信息
	ctx        context.Context    // 上下文对象
	cancel     context.CancelFunc // 取消函数
	pushAPIURL string             // 推送API地址
	httpClient *http.Client       // HTTP客户端
	rng        *rand.Rand         // 随机数生成器
}

// NewFanucCollector 创建FANUC设备采集器
// 功能：根据设备配置创建新的FANUC设备采集器实例
func NewFanucCollector(config DeviceConfig) *FanucCollector {
	ctx, cancel := context.WithCancel(context.Background())

	// 获取推送API地址，优先使用环境变量
	pushAPIURL := os.Getenv("PUSH_API_URL")
	if pushAPIURL == "" {
		pushAPIURL = "http://localhost:8080/api/data/push"
	}

	logger.Infof("[%s:%s] 创建FANUC设备采集器: %s:%d", config.ID, config.IP, config.IP, config.Port)
	logger.Infof("[%s:%s] 推送API地址: %s", config.ID, config.IP, pushAPIURL)

	collector := &FanucCollector{
		config:     config,
		connected:  false,
		collecting: false,
		status: DeviceStatus{
			Connected:    false,
			LastSeen:     time.Now(),
			ErrorCount:   0,
			LastError:    "",
			CollectCount: 0,
			PushCount:    0,
		},
		ctx:        ctx,
		cancel:     cancel,
		pushAPIURL: pushAPIURL,
		httpClient: &http.Client{
			Timeout: 10 * time.Second,
		},
		rng: rand.New(rand.NewSource(time.Now().UnixNano())),
	}

	return collector
}

// Connect 连接到FANUC设备
// 功能：建立与FANUC设备的连接（模拟实现）
func (fc *FanucCollector) Connect() bool {
	fc.mu.Lock()
	defer fc.mu.Unlock()

	logger.Infof("[%s:%s] 开始连接FANUC设备: %s:%d", fc.config.ID, fc.config.IP, fc.config.IP, fc.config.Port)

	// 模拟连接过程和延迟
	time.Sleep(time.Duration(fc.rng.Intn(1000)+500) * time.Millisecond)

	// 模拟连接成功率（90%成功率）
	if fc.rng.Float64() < 0.9 {
		fc.connected = true
		fc.status.Connected = true
		fc.status.LastSeen = time.Now()
		fc.status.LastError = ""

		logger.Infof("[%s:%s] FANUC设备连接成功", fc.config.ID, fc.config.IP)
		return true
	} else {
		fc.connected = false
		fc.status.Connected = false
		fc.status.ErrorCount++
		fc.status.LastError = "模拟连接失败"

		logger.Errorf("[%s:%s] FANUC设备连接失败: %s", fc.config.ID, fc.config.IP, fc.status.LastError)
		return false
	}
}

// Disconnect 断开与FANUC设备的连接
// 功能：断开与FANUC设备的连接，释放资源
func (fc *FanucCollector) Disconnect() {
	fc.mu.Lock()
	defer fc.mu.Unlock()

	if fc.connected {
		logger.Infof("[%s:%s] 断开FANUC设备连接", fc.config.ID, fc.config.IP)
		fc.connected = false
		fc.status.Connected = false
	}
}

// CollectData 采集设备数据
// 功能：从FANUC设备采集各种状态和运行数据（模拟实现）
func (fc *FanucCollector) CollectData() (*DeviceData, error) {
	fc.mu.Lock()
	defer fc.mu.Unlock()

	// 检查设备是否已连接
	if !fc.connected {
		err := fmt.Errorf("设备未连接")
		fc.status.ErrorCount++
		fc.status.LastError = err.Error()
		return nil, err
	}

	logger.Debugf("[%s:%s] 开始采集设备数据", fc.config.ID, fc.config.IP)

	// 模拟数据采集过程和延迟
	time.Sleep(time.Duration(fc.rng.Intn(200)+100) * time.Millisecond)

	// 模拟采集成功率（95%成功率）
	if fc.rng.Float64() < 0.95 {
		// 生成模拟数据
		rawData := fc.generateSimulatedData()

		// 更新状态
		fc.status.LastSeen = time.Now()
		fc.status.CollectCount++
		fc.status.LastError = ""

		// 创建设备数据
		deviceData := &DeviceData{
			DeviceID:   fc.config.ID,
			DeviceName: fc.config.Name,
			DataType:   fc.config.DataType,
			Location:   fc.config.Location,
			Timestamp:  time.Now(),
			Status:     fc.status,
			RawData:    rawData,
		}

		logger.Debugf("[%s:%s] 设备数据采集成功，数据项数量: %d", fc.config.ID, fc.config.IP, len(rawData))
		return deviceData, nil
	} else {
		// 模拟采集失败
		err := fmt.Errorf("模拟数据采集失败")
		fc.status.ErrorCount++
		fc.status.LastError = err.Error()

		logger.Errorf("[%s:%s] 设备数据采集失败: %v", fc.config.ID, fc.config.IP, err)
		return nil, err
	}
}

// generateSimulatedData 生成模拟设备数据
// 功能：生成模拟的FANUC设备数据，用于测试和演示
func (fc *FanucCollector) generateSimulatedData() map[string]interface{} {
	data := make(map[string]interface{})

	// 设备基本状态
	data["device_status"] = map[string]interface{}{
		"power_on":    true,
		"emergency":   false,
		"alarm_count": fc.rng.Intn(3),
		"mode":        []string{"AUTO", "MANUAL", "MDI"}[fc.rng.Intn(3)],
		"run_status":  []string{"STOP", "HOLD", "START", "RESET"}[fc.rng.Intn(4)],
	}

	// 轴位置信息（模拟6轴）
	axes := make([]map[string]interface{}, 6)
	for i := 0; i < 6; i++ {
		axes[i] = map[string]interface{}{
			"axis_name":     fmt.Sprintf("X%d", i+1),
			"absolute_pos":  fc.rng.Float64()*1000 - 500,
			"machine_pos":   fc.rng.Float64()*1000 - 500,
			"relative_pos":  fc.rng.Float64()*100 - 50,
			"distance_left": fc.rng.Float64() * 50,
		}
	}
	data["axes_info"] = axes

	// 主轴信息
	data["spindle_info"] = map[string]interface{}{
		"spindle_speed":    fc.rng.Intn(3000) + 500,
		"spindle_load":     fc.rng.Float64() * 100,
		"spindle_temp":     fc.rng.Float64()*50 + 20,
		"spindle_override": fc.rng.Intn(200) + 50,
	}

	// 进给信息
	data["feed_info"] = map[string]interface{}{
		"feed_rate":     fc.rng.Intn(2000) + 100,
		"feed_override": fc.rng.Intn(200) + 50,
		"rapid_rate":    fc.rng.Intn(10000) + 5000,
	}

	// 程序信息
	data["program_info"] = map[string]interface{}{
		"program_number":  fc.rng.Intn(9999) + 1,
		"program_name":    fmt.Sprintf("PROG_%04d", fc.rng.Intn(9999)+1),
		"sequence_number": fc.rng.Intn(1000) + 1,
		"block_number":    fc.rng.Intn(500) + 1,
		"tool_number":     fc.rng.Intn(20) + 1,
		"tool_offset":     fc.rng.Intn(50) + 1,
	}

	return data
}

// PushData 推送数据到API
// 功能：将采集的设备数据推送到指定的API接口
func (fc *FanucCollector) PushData(data *DeviceData) error {
	fc.mu.Lock()
	defer fc.mu.Unlock()

	logger.Debugf("[%s:%s] 开始推送数据到API: %s", fc.config.ID, fc.config.IP, fc.pushAPIURL)

	// 将数据序列化为JSON
	jsonData, err := json.Marshal(data)
	if err != nil {
		fc.status.ErrorCount++
		fc.status.LastError = fmt.Sprintf("JSON序列化失败: %v", err)
		logger.Errorf("[%s:%s] JSON序列化失败: %v", fc.config.ID, fc.config.IP, err)
		return err
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(fc.ctx, "POST", fc.pushAPIURL, bytes.NewBuffer(jsonData))
	if err != nil {
		fc.status.ErrorCount++
		fc.status.LastError = fmt.Sprintf("创建HTTP请求失败: %v", err)
		logger.Errorf("[%s:%s] 创建HTTP请求失败: %v", fc.config.ID, fc.config.IP, err)
		return err
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "FanucCollector/3.0")

	// 发送HTTP请求
	resp, err := fc.httpClient.Do(req)
	if err != nil {
		fc.status.ErrorCount++
		fc.status.LastError = fmt.Sprintf("HTTP请求失败: %v", err)
		logger.Errorf("[%s:%s] HTTP请求失败: %v", fc.config.ID, fc.config.IP, err)
		return err
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		fc.status.ErrorCount++
		fc.status.LastError = fmt.Sprintf("API返回错误状态码: %d", resp.StatusCode)
		logger.Errorf("[%s:%s] API返回错误状态码: %d", fc.config.ID, fc.config.IP, resp.StatusCode)
		return fmt.Errorf("API返回错误状态码: %d", resp.StatusCode)
	}

	// 更新推送统计
	fc.status.PushCount++
	logger.Debugf("[%s:%s] 数据推送成功，累计推送次数: %d", fc.config.ID, fc.config.IP, fc.status.PushCount)

	return nil
}

// StartCollecting 开始数据采集
// 功能：启动定时数据采集和推送循环
func (fc *FanucCollector) StartCollecting() error {
	fc.mu.Lock()
	defer fc.mu.Unlock()

	if fc.collecting {
		return fmt.Errorf("采集器已在运行中")
	}

	if !fc.connected {
		return fmt.Errorf("设备未连接，无法开始采集")
	}

	fc.collecting = true
	logger.Infof("[%s:%s] 开始数据采集，采集间隔: %dms", fc.config.ID, fc.config.IP, fc.config.CollectInterval)

	// 启动采集协程
	go fc.collectLoop()

	return nil
}

// StopCollecting 停止数据采集
// 功能：停止数据采集循环
func (fc *FanucCollector) StopCollecting() {
	fc.mu.Lock()
	defer fc.mu.Unlock()

	if fc.collecting {
		logger.Infof("[%s:%s] 停止数据采集", fc.config.ID, fc.config.IP)
		fc.collecting = false
		fc.cancel() // 取消上下文，停止采集循环
	}
}

// collectLoop 采集循环
// 功能：定时采集数据并推送到API的主循环
func (fc *FanucCollector) collectLoop() {
	ticker := time.NewTicker(time.Duration(fc.config.CollectInterval) * time.Millisecond)
	defer ticker.Stop()

	for {
		select {
		case <-fc.ctx.Done():
			logger.Infof("[%s:%s] 采集循环已停止", fc.config.ID, fc.config.IP)
			return
		case <-ticker.C:
			// 采集数据
			data, err := fc.CollectData()
			if err != nil {
				logger.Errorf("[%s:%s] 数据采集失败: %v", fc.config.ID, fc.config.IP, err)
				continue
			}

			// 推送数据
			if err := fc.PushData(data); err != nil {
				logger.Errorf("[%s:%s] 数据推送失败: %v", fc.config.ID, fc.config.IP, err)
			}
		}
	}
}

// GetStatus 获取设备状态
// 功能：返回当前设备的连接状态和统计信息
func (fc *FanucCollector) GetStatus() DeviceStatus {
	fc.mu.RLock()
	defer fc.mu.RUnlock()

	return fc.status
}

// IsConnected 检查设备是否已连接
// 功能：返回设备的连接状态
func (fc *FanucCollector) IsConnected() bool {
	fc.mu.RLock()
	defer fc.mu.RUnlock()

	return fc.connected
}

// IsCollecting 检查是否正在采集
// 功能：返回采集器的运行状态
func (fc *FanucCollector) IsCollecting() bool {
	fc.mu.RLock()
	defer fc.mu.RUnlock()

	return fc.collecting
}
