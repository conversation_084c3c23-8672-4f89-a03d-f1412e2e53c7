package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"strconv"
	"syscall"
	"time"

	"fanuc_v3/example/03.process_fanuc/process/collect"
	"shared/logger"
)

// Device 设备信息结构体
// 定义FANUC设备的完整配置信息
type Device struct {
	ID              string `json:"id"`               // 设备唯一标识符
	Name            string `json:"name"`             // 设备名称
	IP              string `json:"ip"`               // 设备IP地址
	Port            int    `json:"port"`             // 设备端口号
	Brand           string `json:"brand"`            // 设备品牌
	Model           string `json:"model"`            // 设备型号
	Location        string `json:"location"`         // 设备位置
	DataType        string `json:"data_type"`        // 数据类型
	Enabled         bool   `json:"enabled"`          // 是否启用
	AutoStart       bool   `json:"auto_start"`       // 是否自动启动
	CollectInterval int    `json:"collect_interval"` // 数据采集间隔(毫秒)
	Timeout         int    `json:"timeout"`          // 连接超时时间(秒)
	RetryCount      int    `json:"retry_count"`      // 重试次数
	RetryDelay      int    `json:"retry_delay"`      // 重试延迟(秒)
}

// APIResponse 统一的API响应结构体
// 提供标准化的响应格式，包含成功状态、消息和数据
type APIResponse struct {
	Success bool        `json:"success"` // 请求是否成功
	Message string      `json:"message"` // 响应消息
	Data    interface{} `json:"data"`    // 响应数据
}

// deviceToConfig 将Device转换为collect.DeviceConfig
// 功能：将从API获取的设备信息转换为采集器需要的配置格式
func deviceToConfig(device Device) collect.DeviceConfig {
	return collect.DeviceConfig{
		ID:              device.ID,
		Name:            device.Name,
		IP:              device.IP,
		Port:            device.Port,
		Brand:           device.Brand,
		Model:           device.Model,
		Location:        device.Location,
		DataType:        device.DataType,
		Enabled:         device.Enabled,
		AutoStart:       device.AutoStart,
		CollectInterval: device.CollectInterval,
		Timeout:         device.Timeout,
		RetryCount:      device.RetryCount,
		RetryDelay:      device.RetryDelay,
	}
}

// checkDeviceExists 检查设备是否存在
// 通过API调用检查指定ID的设备是否存在于系统中
// 参数: deviceID - 设备ID, apiURL - API服务器地址
// 返回: 设备是否存在(bool), 设备信息(Device), 错误信息(error)
func checkDeviceExists(deviceID, apiURL string) (bool, Device, error) {
	// 构建完整的API请求URL
	// 使用设备ID作为路径参数调用获取设备详情接口
	url := fmt.Sprintf("%s/api/public/device/%s", apiURL, deviceID)

	logger.Debugf("正在检查设备是否存在: %s", url)

	// 创建HTTP GET请求
	// 设置5秒超时时间，避免长时间等待
	client := &http.Client{
		Timeout: 5 * time.Second,
	}

	// 发送GET请求获取设备信息
	resp, err := client.Get(url)
	if err != nil {
		logger.Errorf("API请求失败: %v", err)
		return false, Device{}, fmt.Errorf("API请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应体内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Errorf("读取响应失败: %v", err)
		return false, Device{}, fmt.Errorf("读取响应失败: %v", err)
	}

	// 解析JSON响应
	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		logger.Errorf("解析JSON响应失败: %v", err)
		return false, Device{}, fmt.Errorf("解析JSON响应失败: %v", err)
	}

	// 检查API响应状态
	if !apiResp.Success {
		// 设备不存在或其他错误
		logger.Warnf("设备不存在或API返回错误: %s", apiResp.Message)
		return false, Device{}, nil
	}

	// 解析设备数据
	deviceData, err := json.Marshal(apiResp.Data)
	if err != nil {
		logger.Errorf("序列化设备数据失败: %v", err)
		return false, Device{}, fmt.Errorf("序列化设备数据失败: %v", err)
	}

	var device Device
	if err := json.Unmarshal(deviceData, &device); err != nil {
		logger.Errorf("解析设备数据失败: %v", err)
		return false, Device{}, fmt.Errorf("解析设备数据失败: %v", err)
	}

	logger.Infof("设备存在: ID=%s, Name=%s, IP=%s", device.ID, device.Name, device.IP)
	return true, device, nil
}

func main() {
	// 接收命令行参数
	// 定义id参数，用于指定设备ID，必填参数
	idPtr := flag.String("id", "", "设备ID（必填）")
	// 定义API服务器地址参数，如果未提供则从环境变量获取
	apiPtr := flag.String("api", "", "API服务器地址（可从环境变量API_URL获取）")
	// 定义检查间隔参数，如果未提供则从环境变量获取
	intervalPtr := flag.Int("interval", 0, "设备存在性检查间隔（秒）（可从环境变量CHECK_INTERVAL获取）")

	// 解析命令行参数
	flag.Parse()

	// 验证必填参数
	if *idPtr == "" {
		fmt.Println("错误: 设备ID参数(-id)是必填的")
		fmt.Println("使用方法: ./self-process -id=设备ID [-api=API地址] [-interval=检查间隔秒数]")
		fmt.Println("环境变量: API_URL=API服务器地址, CHECK_INTERVAL=检查间隔秒数, PUSH_API_URL=数据推送API地址")
		fmt.Println("示例: ./self-process -id=6a1d3fe8-8d21-416g-b9g5-6e8b2290b86f")
		fmt.Println("示例: API_URL=http://localhost:9105 PUSH_API_URL=http://localhost:8080/api/data/push ./self-process -id=设备ID")
		os.Exit(1)
	}

	// 初始化统一的日志系统
	logDir := "logs"
	if err := os.MkdirAll(logDir, 0755); err != nil {
		fmt.Printf("创建日志目录失败: %v\n", err)
		os.Exit(1)
	}

	// 获取当前时间用于日志文件命名
	now := time.Now()
	dateStr := now.Format("2006-01-02")
	logFile := filepath.Join(logDir, fmt.Sprintf("fanuc-collector_%s-%s.log", dateStr, *idPtr))

	// 配置日志轮转参数
	rotation := logger.LogRotationConfig{
		Enabled:    true, // 启用日志轮转
		MaxSize:    10,   // 单个文件最大10MB
		MaxAge:     7,    // 保留7天
		MaxBackups: 5,    // 保留5个备份文件
		Compress:   true, // 压缩旧文件
	}

	// 初始化日志系统：info级别，文本格式，输出到文件，启用轮转
	logger.InitLoggerWithRotation("info", "text", logFile, rotation)

	// 处理API服务器地址：优先使用命令行参数，其次环境变量，最后默认值
	apiURL := *apiPtr
	var apiSource string
	if apiURL == "" {
		// 从环境变量获取API地址
		if envAPI := os.Getenv("API_URL"); envAPI != "" {
			apiURL = envAPI
			apiSource = "环境变量API_URL"
		} else {
			// 使用默认值
			apiURL = "http://localhost:9105"
			apiSource = "默认值"
		}
	} else {
		apiSource = "命令行参数"
	}

	// 处理检查间隔：优先使用命令行参数，其次环境变量，最后默认值
	checkInterval := *intervalPtr
	var intervalSource string
	if checkInterval == 0 {
		// 从环境变量获取检查间隔
		if envInterval := os.Getenv("CHECK_INTERVAL"); envInterval != "" {
			if interval, err := strconv.Atoi(envInterval); err == nil && interval > 0 {
				checkInterval = interval
				intervalSource = "环境变量CHECK_INTERVAL"
			} else {
				fmt.Printf("警告: 环境变量CHECK_INTERVAL值无效: %s，使用默认值30秒\n", envInterval)
				checkInterval = 30
				intervalSource = "默认值(环境变量无效)"
			}
		} else {
			// 使用默认值
			checkInterval = 30
			intervalSource = "默认值"
		}
	} else {
		intervalSource = "命令行参数"
	}

	// 记录程序启动信息
	logger.Infof("FANUC数据采集器启动，设备ID: %s", *idPtr)
	logger.Infof("API服务器: %s (来源: %s)", apiURL, apiSource)
	logger.Infof("检查间隔: %d秒 (来源: %s)", checkInterval, intervalSource)
	logger.Infof("日志文件路径: %s", logFile)

	fmt.Printf("FANUC数据采集器启动\n")
	fmt.Printf("设备ID: %s\n", *idPtr)
	fmt.Printf("API服务器: %s (来源: %s)\n", apiURL, apiSource)
	fmt.Printf("检查间隔: %d秒 (来源: %s)\n", checkInterval, intervalSource)
	fmt.Printf("日志文件: %s\n", logFile)

	// 初始检查设备是否存在
	logger.Info("开始初始设备存在性检查...")
	exists, device, err := checkDeviceExists(*idPtr, apiURL)
	if err != nil {
		logger.Errorf("初始设备检查失败: %v", err)
		fmt.Printf("初始设备检查失败: %v\n", err)
		os.Exit(1)
	}

	if !exists {
		logger.Errorf("设备不存在，程序退出: ID=%s", *idPtr)
		fmt.Printf("设备不存在，程序退出: ID=%s\n", *idPtr)
		os.Exit(1)
	}

	logger.Infof("设备存在检查通过: ID=%s, Name=%s, IP=%s", device.ID, device.Name, device.IP)
	fmt.Printf("设备存在检查通过: %s (%s)\n", device.Name, device.IP)

	// 将Device转换为DeviceConfig
	config := deviceToConfig(device)

	// 创建FANUC采集器
	logger.Info("创建FANUC设备采集器...")
	collector := collect.NewFanucCollector(config)

	// 连接到设备
	logger.Info("连接到FANUC设备...")
	if !collector.Connect() {
		logger.Error("设备连接失败，程序退出")
		fmt.Println("设备连接失败，程序退出")
		os.Exit(1)
	}

	logger.Info("设备连接成功")
	fmt.Println("设备连接成功")

	// 设置信号处理，优雅关闭
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 启动数据采集
	logger.Info("启动数据采集...")
	if err := collector.StartCollecting(); err != nil {
		logger.Errorf("启动数据采集失败: %v", err)
		fmt.Printf("启动数据采集失败: %v\n", err)
		os.Exit(1)
	}

	logger.Info("数据采集已启动")
	fmt.Println("数据采集已启动，按 Ctrl+C 停止")

	// 创建定时器，用于定期检查设备是否存在
	checkTicker := time.NewTicker(time.Duration(checkInterval) * time.Second)
	defer checkTicker.Stop()

	// 主循环：等待信号或定期检查设备存在性
	for {
		select {
		case sig := <-sigChan:
			logger.Infof("接收到信号: %v，开始优雅关闭", sig)
			fmt.Printf("接收到信号: %v，开始优雅关闭...\n", sig)

			// 停止数据采集
			collector.StopCollecting()

			// 断开设备连接
			collector.Disconnect()

			logger.Info("程序已优雅关闭")
			fmt.Println("程序已优雅关闭")
			return

		case <-checkTicker.C:
			logger.Infof("执行定时设备存在性检查 (间隔: %d秒)", checkInterval)
			exists, currentDevice, err := checkDeviceExists(*idPtr, apiURL)

			if err != nil {
				logger.Errorf("设备检查失败: %v", err)
				// 检查失败不退出，继续运行，可能是网络临时问题
			} else if !exists {
				logger.Errorf("设备不存在，程序退出: ID=%s", *idPtr)
				fmt.Printf("设备不存在，程序退出: ID=%s\n", *idPtr)

				// 停止数据采集
				collector.StopCollecting()

				// 断开设备连接
				collector.Disconnect()

				os.Exit(0) // 正常退出，因为设备被删除
			} else {
				logger.Infof("设备存在性检查通过: %s (%s)", currentDevice.Name, currentDevice.IP)

				// 显示采集器状态
				status := collector.GetStatus()
				logger.Infof("采集器状态 - 连接: %v, 采集次数: %d, 推送次数: %d, 错误次数: %d",
					status.Connected, status.CollectCount, status.PushCount, status.ErrorCount)
			}
		}
	}
}
