# Self-Process 设备监控程序

## 功能概述

这是一个增强版的设备监控程序，具有以下主要功能：

1. **设备ID参数支持** - 通过设备ID从API获取设备信息
2. **定时设备存在性检查** - 定期检查设备是否仍存在于系统中
3. **自动退出机制** - 当设备被删除时自动退出程序
4. **企业级日志记录** - 详细的中文注释和日志记录

## 命令行参数

| 参数 | 类型 | 默认值 | 必填 | 环境变量 | 说明 |
|------|------|--------|------|----------|------|
| `-id` | string | - | ✅ | - | 设备ID，用于从API获取设备信息 |
| `-ip` | string | 127.0.0.1 | ❌ | - | 设备IP地址（用于显示） |
| `-api` | string | http://localhost:9105 | ❌ | `API_URL` | API服务器地址 |
| `-interval` | int | 30 | ❌ | `CHECK_INTERVAL` | 设备存在性检查间隔（秒） |

## 环境变量支持

程序支持通过环境变量配置参数，优先级如下：
1. **命令行参数** - 最高优先级
2. **环境变量** - 中等优先级
3. **默认值** - 最低优先级

### 支持的环境变量

| 环境变量 | 对应参数 | 说明 | 示例 |
|----------|----------|------|------|
| `API_URL` | `-api` | API服务器地址 | `http://localhost:9105` |
| `CHECK_INTERVAL` | `-interval` | 检查间隔（秒） | `30` |

### 环境变量验证

- `CHECK_INTERVAL` 必须是正整数，无效值将使用默认值30秒
- 无效的环境变量值会显示警告信息

## 使用方法

### 基本用法
```bash
./self-process -id=6a1d3fe8-8d21-416g-b9g5-6e8b2290b86f
```

### 使用命令行参数
```bash
./self-process \
  -id=6a1d3fe8-8d21-416g-b9g5-6e8b2290b86f \
  -ip=************ \
  -api=http://localhost:9105 \
  -interval=30
```

### 使用环境变量
```bash
# 设置环境变量
export API_URL=http://localhost:9105
export CHECK_INTERVAL=60

# 运行程序
./self-process -id=6a1d3fe8-8d21-416g-b9g5-6e8b2290b86f
```

### 混合使用（命令行参数覆盖环境变量）
```bash
# 环境变量设置为60秒，但命令行参数设置为10秒
API_URL=http://localhost:9105 CHECK_INTERVAL=60 \
./self-process -id=6a1d3fe8-8d21-416g-b9g5-6e8b2290b86f -interval=10
```

### 一次性环境变量
```bash
API_URL=http://localhost:9105 CHECK_INTERVAL=5 \
./self-process -id=6a1d3fe8-8d21-416g-b9g5-6e8b2290b86f
```

## 工作流程

1. **参数验证** - 检查必填的设备ID参数
2. **初始设备检查** - 启动时验证设备是否存在
3. **程序主循环** - 执行随机次数的循环处理
4. **定时检查** - 在主循环中定期检查设备存在性
5. **自动退出** - 当设备不存在时自动退出

## API接口

程序使用以下API接口：

- `GET /api/public/device/{id}` - 获取指定ID的设备信息

### API响应格式

```json
{
  "success": true,
  "message": "获取设备配置成功",
  "data": {
    "id": "6a1d3fe8-8d21-416g-b9g5-6e8b2290b86f",
    "name": "#13 SB-25R",
    "ip": "************",
    "port": 8193,
    "brand": "Star",
    "model": "SB-25R typeG",
    "location": "龙光园区",
    "data_type": "fanuc_30i",
    "enabled": true,
    "auto_start": true,
    "collect_interval": 1500,
    "timeout": 15,
    "retry_count": 5,
    "retry_delay": 8
  }
}
```

## 日志文件

日志文件命名格式：`logs/self-process_{日期}-{设备ID}.log`

示例：`logs/self-process_2025-07-29-6a1d3fe8-8d21-416g-b9g5-6e8b2290b86f.log`

### 日志内容包括：
- 程序启动信息
- 设备存在性检查结果
- 循环处理进度
- 错误和警告信息
- 程序退出信息

## 错误处理

### 常见错误情况：

1. **设备ID未提供**
   ```
   错误: 设备ID参数(-id)是必填的
   使用方法: ./self-process -id=设备ID [-ip=设备IP] [-api=API地址] [-interval=检查间隔秒数]
   环境变量: API_URL=API服务器地址, CHECK_INTERVAL=检查间隔秒数
   ```

2. **设备不存在**
   ```
   设备不存在，程序退出: ID=nonexistent-device-id
   ```

3. **API连接失败**
   ```
   API请求失败: dial tcp [::1]:9105: connect: connection refused
   ```

4. **环境变量值无效**
   ```
   警告: 环境变量CHECK_INTERVAL值无效: invalid，使用默认值30秒
   ```

## 测试脚本

提供了测试脚本 `test_device_removal.sh` 用于演示设备删除后程序自动退出的功能：

```bash
./test_device_removal.sh
```

该脚本会：
1. 创建一个测试设备
2. 启动监控程序
3. 删除设备
4. 验证程序自动退出

## 编译和运行

### 编译程序
```bash
go build -o self-process self-process.go
```

### 运行程序
```bash
./self-process -id=你的设备ID
```

## 技术特性

- **并发安全** - 使用Go的select语句实现非阻塞定时检查
- **资源管理** - 正确关闭HTTP连接和定时器
- **错误恢复** - 网络错误不会导致程序退出，只有设备删除才会退出
- **日志轮转** - 支持日志文件大小限制和自动压缩
- **企业级代码** - 详细的中文注释和错误处理

## 注意事项

1. 确保API服务器在程序启动前已经运行
2. 设备ID必须在API服务器中存在
3. 程序会在设备被删除时自动退出（退出码0）
4. 网络临时故障不会导致程序退出，只会记录错误日志
5. 环境变量优先级低于命令行参数
6. 无效的环境变量值会显示警告并使用默认值
7. 程序启动时会显示参数来源（命令行参数/环境变量/默认值）
