# FANUC v3 自执行进程管理器 - 环境变量配置示例
# 复制此文件为 .env 并根据实际环境修改配置

# ================================
# API 配置
# ================================

# 设备配置API地址
# 本地开发环境
DEVICE_API_URL=http://localhost:9105/api/public/devices/configs

# Docker环境示例
# DEVICE_API_URL=http://mdc_server_api:9005/api/public/devices/configs

# 生产环境示例
# DEVICE_API_URL=http://*************:9005/api/public/devices/configs

# ================================
# 定时配置
# ================================

# 设备列表刷新间隔（秒）
# 建议值：30-300秒之间
DEVICE_REFRESH_INTERVAL=30

# 开发环境可以设置更短的间隔进行测试
# DEVICE_REFRESH_INTERVAL=10

# 生产环境建议设置较长的间隔以减少API负载
# DEVICE_REFRESH_INTERVAL=60

# ================================
# 使用说明
# ================================

# 1. 复制此文件为 .env
#    cp .env.example .env

# 2. 根据实际环境修改配置值

# 3. 加载环境变量
#    source .env

# 4. 启动程序
#    cd manage && ./self-execute

# ================================
# Docker 环境变量示例
# ================================

# 在 docker-compose.yml 中使用：
# environment:
#   - DEVICE_API_URL=http://mdc_server_api:9005/api/public/devices/configs
#   - DEVICE_REFRESH_INTERVAL=60

# 在 Dockerfile 中使用：
# ENV DEVICE_API_URL=http://mdc_server_api:9005/api/public/devices/configs
# ENV DEVICE_REFRESH_INTERVAL=60

# ================================
# Kubernetes 环境变量示例
# ================================

# 在 deployment.yaml 中使用：
# env:
# - name: DEVICE_API_URL
#   value: "http://mdc-server-api:9005/api/public/devices/configs"
# - name: DEVICE_REFRESH_INTERVAL
#   value: "60"
