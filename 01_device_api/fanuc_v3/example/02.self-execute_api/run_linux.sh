#!/bin/bash

# Linux 运行脚本
# 在 Linux x86_64 平台运行进程管理器

echo "========================================="
echo "Linux x86_64 进程管理器启动脚本"
echo "========================================="

# 检查可执行文件是否存在
MANAGE_EXEC="./self-manage_linux"
PROCESS_EXEC="./self-process_linux"

if [ ! -f "$MANAGE_EXEC" ]; then
    echo "错误: 未找到管理器可执行文件 $MANAGE_EXEC"
    echo "请先运行编译脚本: ./build_all_linux.sh"
    exit 1
fi

if [ ! -f "$PROCESS_EXEC" ]; then
    echo "错误: 未找到子进程可执行文件 $PROCESS_EXEC"
    echo "请先运行编译脚本: ./build_all_linux.sh"
    exit 1
fi

# 检查执行权限
if [ ! -x "$MANAGE_EXEC" ]; then
    echo "添加执行权限: $MANAGE_EXEC"
    chmod +x "$MANAGE_EXEC"
fi

if [ ! -x "$PROCESS_EXEC" ]; then
    echo "添加执行权限: $PROCESS_EXEC"
    chmod +x "$PROCESS_EXEC"
fi

# 显示文件信息
echo "可执行文件信息:"
ls -la *_linux
echo ""

# 显示系统信息
echo "系统信息:"
echo "  操作系统: $(uname -s)"
echo "  架构: $(uname -m)"
echo "  内核版本: $(uname -r)"
echo ""

# 创建日志目录
if [ ! -d "logs" ]; then
    mkdir -p logs
    echo "创建日志目录: logs/"
fi

echo "启动进程管理器..."
echo "日志将保存在 logs/ 目录下"
echo "按 Ctrl+C 停止管理器"
echo ""

# 运行管理器
exec "$MANAGE_EXEC"
