package main

import (
	"flag"
	"fmt"
	"math/rand"
	"os"
	"path/filepath"
	"time"

	"shared/logger"
)

func main() {
	// 接收命令行参数 -ip
	// 定义ip参数，默认值为"127.0.0.1"，用法说明
	ipPtr := flag.String("ip", "127.0.0.1", "设备IP地址")

	// 解析命令行参数
	flag.Parse()

	// 初始化统一的日志系统
	// 使用shared/logger包，支持日志轮转和多种输出格式
	logDir := "logs"
	if err := os.MkdirAll(logDir, 0755); err != nil {
		fmt.Printf("创建日志目录失败: %v\n", err)
		os.Exit(1)
	}

	// 获取当前时间用于日志文件命名
	now := time.Now()
	dateStr := now.Format("2006-01-02")
	logFile := filepath.Join(logDir, fmt.Sprintf("self-process_%s-%s.log", dateStr, *ipPtr))

	// 配置日志轮转参数
	rotation := logger.LogRotationConfig{
		Enabled:    true, // 启用日志轮转
		MaxSize:    10,   // 单个文件最大10MB
		MaxAge:     7,    // 保留7天
		MaxBackups: 5,    // 保留5个备份文件
		Compress:   true, // 压缩旧文件
	}

	// 初始化日志系统：info级别，文本格式，输出到文件，启用轮转
	logger.InitLoggerWithRotation("info", "text", logFile, rotation)

	// 记录程序启动信息
	logger.Infof("程序启动，接收到的IP参数: %s", *ipPtr)
	logger.Infof("日志文件路径: %s", logFile)
	fmt.Printf("接收到的IP参数: %s\n", *ipPtr)
	fmt.Printf("日志文件: %s\n", logFile)

	// 设置随机数种子，确保每次运行产生不同的随机数
	rand.Seed(time.Now().UnixNano())
	logger.Info("随机数种子设置完成")

	// 获取一个 50 至 100 的随机数
	// rand.Intn(51) 生成 0-50 的随机数，加上 50 得到 50-100 的范围
	randomNum := rand.Intn(51) + 50

	// 记录生成的随机数信息
	logger.Infof("生成的随机数: %d，将循环 %d 次", randomNum, randomNum)
	fmt.Printf("生成的随机数: %d，将循环 %d 次\n", randomNum, randomNum)

	// 记录循环开始
	logger.Infof("开始循环处理，目标IP: %s", *ipPtr)

	// 循环这个随机数次
	for i := 1; i <= randomNum; i++ {
		// 记录每次循环的详细信息
		logger.Debugf("第 %d 次循环，随机数: %d，IP: %s", i, randomNum, *ipPtr)

		// 每10次循环记录一次info级别日志，避免日志过多
		if i%10 == 0 || i == 1 || i == randomNum {
			logger.Infof("循环进度: %d/%d，IP: %s", i, randomNum, *ipPtr)
			fmt.Printf("第 %d 次循环，随机数: %d，IP: %s\n", i, randomNum, *ipPtr)
		}

		// Sleep 1 秒
		time.Sleep(1 * time.Second)

		// 模拟中间检查点（演示警告日志功能）
		if i == randomNum/2 {
			logger.Warnf("到达中间检查点，当前进度: %d/%d，IP: %s", i, randomNum, *ipPtr)
		}
	}

	// 记录程序完成信息
	logger.Infof("循环完成！总共执行了 %d 次循环，IP: %s", randomNum, *ipPtr)
	fmt.Println("循环完成！")
}
