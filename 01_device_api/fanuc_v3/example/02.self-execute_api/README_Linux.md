# Linux x86_64 部署说明

## 概述

本项目提供了类似 supervisord 的进程管理器，支持自动重启功能。包含两个主要组件：

- `self-manage_linux`: 进程管理器，负责启动和监控子进程
- `self-process_linux`: 子进程程序，模拟设备监控任务

## 编译

### 在 macOS/Windows 上交叉编译

```bash
# 编译所有组件
./build_all_linux.sh

# 或者分别编译
cd process && bash build_linux.sh && cd ..
cd manage && bash build_linux.sh && cd ..
```

### 编译输出

编译成功后会生成以下文件：
- `self-manage_linux` - 进程管理器 (约2.1MB)
- `self-process_linux` - 子进程程序 (约2.0MB)

## 部署到 Linux 服务器

### 1. 文件传输

将以下文件传输到 Linux x86_64 服务器：

```bash
# 必需文件
self-manage_linux
self-process_linux
run_linux.sh

# 可选文件
README_Linux.md
```

### 2. 设置权限

```bash
chmod +x self-manage_linux
chmod +x self-process_linux
chmod +x run_linux.sh
```

### 3. 运行程序

```bash
# 方式1: 使用运行脚本（推荐）
./run_linux.sh

# 方式2: 直接运行管理器
./self-manage_linux
```

## 功能特性

### 进程管理器特性

- ✅ 自动启动多个子进程实例
- ✅ 监控子进程状态
- ✅ 自动重启异常退出的进程
- ✅ 可配置的重启延迟和次数限制
- ✅ 企业级日志记录和轮转
- ✅ 按设备IP分离日志文件

### 默认配置

- 监控设备IP: `************`, `************`, `************`
- 重启延迟: 3秒（************为5秒）
- 最大重启次数: 无限制（************为10次）
- 日志轮转: 启用，管理器20MB/30天，子进程10MB/7天

## 日志文件

程序运行后会在 `logs/` 目录下生成以下日志文件：

```
logs/
├── self-manage_2025-07-18.log                    # 管理器日志
├── self-process_2025-07-18-************.log     # 设备1日志
├── self-process_2025-07-18-************.log     # 设备2日志
└── self-process_2025-07-18-************.log     # 设备3日志
```

## 系统要求

- 操作系统: Linux x86_64
- 内核版本: 2.6.32+ (支持大多数现代Linux发行版)
- 内存: 最少64MB可用内存
- 磁盘: 最少100MB可用空间（用于程序和日志）

## 支持的Linux发行版

- Ubuntu 16.04+
- CentOS 7+
- RHEL 7+
- Debian 9+
- SUSE Linux Enterprise 12+
- Amazon Linux 2

## 停止程序

```bash
# 按 Ctrl+C 停止管理器
# 或者发送SIGTERM信号
kill -TERM <管理器PID>
```

## 故障排查

### 1. 权限问题

```bash
# 确保文件有执行权限
ls -la *_linux
chmod +x *_linux
```

### 2. 依赖问题

程序使用静态链接编译，无需额外依赖库。

### 3. 端口冲突

程序不使用网络端口，无端口冲突问题。

### 4. 日志查看

```bash
# 查看管理器日志
tail -f logs/self-manage_*.log

# 查看特定设备日志
tail -f logs/self-process_*-************.log
```

## 自定义配置

如需修改监控的设备IP或重启策略，请编辑源码中的配置并重新编译：

```go
// 在 manage/self-execute.go 中修改
deviceIPs := []string{
    "你的设备IP1",
    "你的设备IP2",
    // ...
}
```

## 技术支持

如遇问题，请检查：
1. 系统架构是否为 x86_64
2. 文件权限是否正确
3. 日志文件中的错误信息
4. 系统资源是否充足
