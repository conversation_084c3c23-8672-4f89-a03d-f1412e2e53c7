package main

import (
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"sync"
	"time"

	"shared/logger"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

// Device 设备信息结构体
// 定义FANUC设备的完整配置信息
type Device struct {
	ID              string `json:"id"`               // 设备唯一标识符
	Name            string `json:"name"`             // 设备名称
	IP              string `json:"ip"`               // 设备IP地址
	Port            int    `json:"port"`             // 设备端口号
	Brand           string `json:"brand"`            // 设备品牌
	Model           string `json:"model"`            // 设备型号
	Location        string `json:"location"`         // 设备位置
	DataType        string `json:"data_type"`        // 数据类型
	Enabled         bool   `json:"enabled"`          // 是否启用
	AutoStart       bool   `json:"auto_start"`       // 是否自动启动
	CollectInterval int    `json:"collect_interval"` // 数据采集间隔(毫秒)
	Timeout         int    `json:"timeout"`          // 连接超时时间(秒)
	RetryCount      int    `json:"retry_count"`      // 重试次数
	RetryDelay      int    `json:"retry_delay"`      // 重试延迟(秒)
}

// APIResponse 统一的API响应结构体
// 提供标准化的响应格式，包含成功状态、消息和数据
type APIResponse struct {
	Success bool        `json:"success"` // 请求是否成功
	Message string      `json:"message"` // 响应消息
	Data    interface{} `json:"data"`    // 响应数据
}

// 全局设备列表变量，用于动态管理设备
var (
	devices      []Device
	devicesMutex sync.RWMutex // 保护设备列表的读写锁
)

// 初始化设备列表
func init() {
	devices = []Device{

		{
			ID:              "6a1d3fe8-8d21-416g-b9g5-6e8b2290b86f",
			Name:            "#13 SB-25R",
			IP:              "************",
			Port:            8193,
			Brand:           "Star",
			Model:           "SB-25R typeG",
			Location:        "龙光园区",
			DataType:        "fanuc_30i",
			Enabled:         true,
			AutoStart:       true,
			CollectInterval: 1500,
			Timeout:         15,
			RetryCount:      5,
			RetryDelay:      8,
		},

		{
			ID:              "8c3f5h0a-af43-638i-d1i7-8g0d4412d08h",
			Name:            "#15 SB-35R",
			IP:              "************",
			Port:            8193,
			Brand:           "Star",
			Model:           "SB-35R typeG",
			Location:        "龙光园区",
			DataType:        "fanuc_30i",
			Enabled:         true,
			AutoStart:       true,
			CollectInterval: 1200,
			Timeout:         12,
			RetryCount:      4,
			RetryDelay:      6,
		},
	}
}

// getDeviceList 获取设备列表数据
// 返回当前的FANUC设备配置列表，支持动态增删
func getDeviceList() []Device {
	devicesMutex.RLock()
	defer devicesMutex.RUnlock()

	// 创建副本以避免并发访问问题
	devicesCopy := make([]Device, len(devices))
	copy(devicesCopy, devices)
	return devicesCopy
}

// generateDeviceID 生成新的设备ID
// 使用时间戳和随机数生成唯一ID
func generateDeviceID() string {
	// 使用时间戳 + 随机数的方式生成ID
	timestamp := time.Now().UnixNano()
	return fmt.Sprintf("device-%d", timestamp)
}

// getDevicesHandler 处理获取设备列表的API请求
// GET /api/v1/devices
func getDevicesHandler(c *gin.Context) {
	logger.Info("收到获取设备列表请求")

	// 获取设备列表数据
	devices := getDeviceList()

	// 构建响应数据
	response := APIResponse{
		Success: true,
		Message: "获取设备配置成功",
		Data:    devices,
	}

	logger.Infof("返回设备列表，共 %d 个设备", len(devices))

	// 返回JSON响应
	c.JSON(http.StatusOK, response)
}

func getDeviceByIdHandler(c *gin.Context) {
	deviceID := c.Param("id")
	logger.Infof("收到获取设备请求: %s", deviceID)

	// 获取设备列表数据
	devices := getDeviceList()

	// 查找指定ID的设备
	var device Device
	found := false
	for _, d := range devices {
		if d.ID == deviceID {
			device = d
			found = true
			break
		}
	}

	if !found {
		logger.Warnf("设备不存在: %s", deviceID)
		c.JSON(http.StatusNotFound, APIResponse{
			Success: false,
			Message: "设备不存在",
		})
		return
	}

	// 构建响应数据
	response := APIResponse{
		Success: true,
		Message: "获取设备配置成功",
		Data:    device,
	}

	logger.Infof("返回设备信息: %s", device.Name)

	// 返回JSON响应
	c.JSON(http.StatusOK, response)
}

// addDeviceHandler 处理添加新设备的API请求
// POST /api/public/devices
func addDeviceHandler(c *gin.Context) {
	logger.Info("收到添加设备请求")

	// 通过 form 参数获取新设备数据，并构建 Device 结构体
	name := c.PostForm("name")
	ip := c.PostForm("ip")
	portStr := c.DefaultPostForm("port", "8193")
	brand := c.DefaultPostForm("brand", "FANUC")
	model := c.PostForm("model")
	location := c.PostForm("location")
	dataType := c.DefaultPostForm("data_type", "fanuc_30i")
	enabledStr := c.DefaultPostForm("enabled", "true")
	autoStartStr := c.DefaultPostForm("auto_start", "true")
	collectIntervalStr := c.DefaultPostForm("collect_interval", "1000")
	timeoutStr := c.DefaultPostForm("timeout", "10")
	retryCountStr := c.DefaultPostForm("retry_count", "3")
	retryDelayStr := c.DefaultPostForm("retry_delay", "5")

	// 验证必需字段
	if name == "" || ip == "" {
		response := APIResponse{
			Success: false,
			Message: "设备名称和IP地址为必填项",
			Data:    nil,
		}
		logger.Warnf("添加设备失败: 缺少必需字段 - name: %s, ip: %s", name, ip)
		c.JSON(http.StatusBadRequest, response)
		return
	}

	// 转换数值类型字段
	port, err := strconv.Atoi(portStr)
	if err != nil {
		port = 8193
	}

	enabled, err := strconv.ParseBool(enabledStr)
	if err != nil {
		enabled = true
	}

	autoStart, err := strconv.ParseBool(autoStartStr)
	if err != nil {
		autoStart = true
	}

	collectInterval, err := strconv.Atoi(collectIntervalStr)
	if err != nil {
		collectInterval = 1000
	}

	timeout, err := strconv.Atoi(timeoutStr)
	if err != nil {
		timeout = 10
	}

	retryCount, err := strconv.Atoi(retryCountStr)
	if err != nil {
		retryCount = 3
	}

	retryDelay, err := strconv.Atoi(retryDelayStr)
	if err != nil {
		retryDelay = 5
	}

	// 生成新的设备ID
	deviceID := generateDeviceID()

	// 构建设备对象
	device := Device{
		ID:              deviceID,
		Name:            name,
		IP:              ip,
		Port:            port,
		Brand:           brand,
		Model:           model,
		Location:        location,
		DataType:        dataType,
		Enabled:         enabled,
		AutoStart:       autoStart,
		CollectInterval: collectInterval,
		Timeout:         timeout,
		RetryCount:      retryCount,
		RetryDelay:      retryDelay,
	}

	// 检查IP地址是否已存在
	devicesMutex.Lock()
	defer devicesMutex.Unlock()

	for _, existingDevice := range devices {
		if existingDevice.IP == ip {
			response := APIResponse{
				Success: false,
				Message: fmt.Sprintf("IP地址 %s 已被设备 %s 使用", ip, existingDevice.Name),
				Data:    nil,
			}
			logger.Warnf("添加设备失败: IP地址冲突 - %s", ip)
			c.JSON(http.StatusConflict, response)
			return
		}
	}

	// 添加新设备到设备列表
	devices = append(devices, device)

	response := APIResponse{
		Success: true,
		Message: "设备添加成功",
		Data:    device,
	}

	logger.Infof("成功添加新设备: ID=%s, Name=%s, IP=%s", device.ID, device.Name, device.IP)
	c.JSON(http.StatusOK, response)
}

// removeDeviceHandler 处理删除设备的API请求
// DELETE /api/public/devices/:id
func removeDeviceHandler(c *gin.Context) {
	logger.Info("收到删除设备请求")

	// 从API请求中获取删除的设备ID
	deviceID := c.Param("id")

	// 验证设备ID是否提供
	if deviceID == "" {
		response := APIResponse{
			Success: false,
			Message: "设备ID不能为空",
			Data:    nil,
		}
		logger.Warn("删除设备失败: 设备ID为空")
		c.JSON(http.StatusBadRequest, response)
		return
	}

	// 从设备列表中移除
	devicesMutex.Lock()
	defer devicesMutex.Unlock()

	var removedDevice *Device
	for i, d := range devices {
		if d.ID == deviceID {
			// 保存被删除的设备信息用于响应
			removedDevice = &d
			// 从切片中移除设备
			devices = append(devices[:i], devices[i+1:]...)
			break
		}
	}

	// 检查是否找到并删除了设备
	if removedDevice == nil {
		response := APIResponse{
			Success: false,
			Message: fmt.Sprintf("未找到设备ID: %s", deviceID),
			Data:    nil,
		}
		logger.Warnf("删除设备失败: 未找到设备ID - %s", deviceID)
		c.JSON(http.StatusNotFound, response)
		return
	}

	// 返回成功响应
	response := APIResponse{
		Success: true,
		Message: "移除设备成功",
		Data:    removedDevice,
	}

	logger.Infof("成功删除设备: ID=%s, Name=%s, IP=%s", removedDevice.ID, removedDevice.Name, removedDevice.IP)
	c.JSON(http.StatusOK, response)
}

// pushDataHandler 处理推送数据的API请求
func pushDataHandler(c *gin.Context) {

	// 获取用户提效的数据
	data := c.Request.Body
	defer data.Close()

	// 读取数据
	body, err := io.ReadAll(data)
	if err != nil {
		logger.Errorf("读取请求数据失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "读取请求数据失败"})
		return
	}

	// 打印数据
	logger.Infof("收到推送数据: %s", string(body))

	// 返回成功响应
	c.JSON(http.StatusOK, gin.H{"message": "数据已接收"})
}

// healthHandler 健康检查接口
// GET /api/public/health
func healthHandler(c *gin.Context) {
	response := APIResponse{
		Success: true,
		Message: "API服务运行正常",
		Data: map[string]interface{}{
			"status":    "healthy",
			"timestamp": time.Now().Format("2006-01-02 15:04:05"),
			"version":   "1.0.0",
		},
	}

	c.JSON(http.StatusOK, response)
}

// setupCORS 配置跨域中间件
// 允许前端应用跨域访问API
func setupCORS() gin.HandlerFunc {
	config := cors.DefaultConfig()

	// 允许所有来源（生产环境应该限制具体域名）
	config.AllowAllOrigins = true

	// 允许的HTTP方法
	config.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}

	// 允许的请求头
	config.AllowHeaders = []string{
		"Origin",
		"Content-Type",
		"Content-Length",
		"Accept-Encoding",
		"X-CSRF-Token",
		"Authorization",
		"Accept",
		"Cache-Control",
		"X-Requested-With",
	}

	// 允许暴露的响应头
	config.ExposeHeaders = []string{"Content-Length"}

	// 预检请求的缓存时间
	config.MaxAge = 12 * time.Hour

	return cors.New(config)
}

// setupRoutes 设置API路由
// 配置所有的API端点和中间件
func setupRoutes() *gin.Engine {
	// 设置Gin模式（生产环境使用release模式）
	gin.SetMode(gin.ReleaseMode)

	// 创建Gin引擎
	router := gin.New()

	// 添加中间件
	router.Use(gin.Logger())   // 日志中间件
	router.Use(gin.Recovery()) // 恢复中间件
	router.Use(setupCORS())    // 跨域中间件

	// API public 路由组
	v1 := router.Group("/api/public")
	{
		// 设备相关接口
		v1.GET("/devices/configs", getDevicesHandler)
		v1.GET("/device/:id", getDeviceByIdHandler)

		// 设备管理接口
		v1.POST("/device", addDeviceHandler)          // 添加设备
		v1.DELETE("/device/:id", removeDeviceHandler) // 删除设备

		// PUSH DATA
		v1.POST("/data/push", pushDataHandler)

		// 健康检查接口
		v1.GET("/health", healthHandler)
	}

	// 根路径重定向到健康检查
	router.GET("/", func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, "/api/v1/health")
	})

	return router
}

// main 主函数
// 初始化日志系统，启动Gin RESTful API服务器
func main() {
	// 初始化日志系统
	// 创建日志目录
	logDir := "logs"
	if err := os.MkdirAll(logDir, 0755); err != nil {
		fmt.Printf("创建日志目录失败: %v\n", err)
		os.Exit(1)
	}

	// 获取当前时间用于日志文件命名
	now := time.Now()
	dateStr := now.Format("2006-01-02")
	logFile := filepath.Join(logDir, fmt.Sprintf("api-server_%s.log", dateStr))

	// 配置日志轮转参数
	rotation := logger.LogRotationConfig{
		Enabled:    true, // 启用日志轮转
		MaxSize:    50,   // 单个文件最大50MB
		MaxAge:     30,   // 保留30天
		MaxBackups: 10,   // 保留10个备份文件
		Compress:   true, // 压缩旧文件
	}

	// 初始化日志系统：info级别，文本格式，输出到文件，启用轮转
	logger.InitLoggerWithRotation("info", "text", logFile, rotation)

	// 记录服务启动信息
	logger.Info("========== FANUC API 服务器启动 ==========")
	logger.Infof("日志文件路径: %s", logFile)

	// 设置服务器配置
	port := ":9105"
	if envPort := os.Getenv("PORT"); envPort != "" {
		port = ":" + envPort
	}

	logger.Infof("服务器端口: %s", port)
	fmt.Printf("FANUC API 服务器启动中...\n")
	fmt.Printf("端口: %s\n", port)
	fmt.Printf("日志文件: %s\n", logFile)

	// 设置路由
	router := setupRoutes()

	// 记录可用的API端点
	logger.Info("可用的API端点:")
	logger.Info("  GET  /                    - 重定向到健康检查")
	logger.Info("  GET  /api/public/health       - 健康检查")
	logger.Info("  GET    /api/public/devices/configs    - 获取设备列表")
	logger.Info("  GET    /api/public/device/:id         - 获取单个设备详情")
	logger.Info("  POST   /api/public/devices            - 添加新设备")
	logger.Info("  DELETE /api/public/devices/:id        - 删除设备")

	fmt.Println("\n可用的API端点:")
	fmt.Printf("  GET    http://localhost%s/                              - 重定向到健康检查\n", port)
	fmt.Printf("  GET    http://localhost%s/api/public/health             - 健康检查\n", port)
	fmt.Printf("  GET    http://localhost%s/api/public/devices/configs    - 获取设备列表\n", port)
	fmt.Printf("  GET    http://localhost%s/api/public/device/:id         - 获取单个设备详情\n", port)
	fmt.Printf("  POST   http://localhost%s/api/public/devices            - 添加新设备\n", port)
	fmt.Printf("  DELETE http://localhost%s/api/public/devices/:id        - 删除设备\n", port)
	fmt.Println("\n按 Ctrl+C 停止服务器")

	// 启动HTTP服务器
	logger.Infof("HTTP服务器启动在端口 %s", port)
	if err := router.Run(port); err != nil {
		logger.Fatalf("启动服务器失败: %v", err)
	}
}
