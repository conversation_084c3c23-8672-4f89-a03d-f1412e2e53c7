#!/bin/bash

# 统一的 Linux x86_64 交叉编译脚本
# 一次性编译所有组件：管理器和子进程

echo "========================================="
echo "开始编译 Linux x86_64 版本的所有组件"
echo "========================================="

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "错误: 未找到Go编译器，请先安装Go"
    exit 1
fi

echo "Go版本信息:"
go version

# 设置交叉编译环境变量
export GOOS=linux      # 目标操作系统
export GOARCH=386    # 目标架构 (x86_64)
export CGO_ENABLED=0   # 禁用CGO，确保静态链接

echo ""
echo "交叉编译配置:"
echo "  目标系统: $GOOS"
echo "  目标架构: $GOARCH"
echo "  CGO状态: $CGO_ENABLED"
echo ""

# 编译计数器
SUCCESS_COUNT=0
TOTAL_COUNT=2

# 1. 编译子进程程序
echo "========================================="
echo "1. 编译子进程程序 (self-process)"
echo "========================================="

cd process
if bash build_linux.sh; then
    echo "✅ 子进程程序编译成功"
    SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
else
    echo "❌ 子进程程序编译失败"
fi
cd ..

echo ""

# 2. 编译管理器程序
echo "========================================="
echo "2. 编译管理器程序 (self-manage)"
echo "========================================="

cd manage
if bash build_linux.sh; then
    echo "✅ 管理器程序编译成功"
    SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
else
    echo "❌ 管理器程序编译失败"
fi
cd ..

echo ""
echo "========================================="
echo "编译结果汇总"
echo "========================================="

if [ $SUCCESS_COUNT -eq $TOTAL_COUNT ]; then
    echo "🎉 所有组件编译成功! ($SUCCESS_COUNT/$TOTAL_COUNT)"
    
    echo ""
    echo "生成的Linux可执行文件:"
    ls -la *_linux 2>/dev/null || echo "  (未找到Linux可执行文件)"
    
    echo ""
    echo "文件详细信息:"
    for file in *_linux; do
        if [ -f "$file" ]; then
            echo "  $file:"
            file "$file" | sed 's/^/    /'
            ls -lh "$file" | awk '{print "    大小: " $5 ", 修改时间: " $6 " " $7 " " $8}'
        fi
    done
    
    echo ""
    echo "使用说明:"
    echo "  1. 将生成的 *_linux 文件传输到 Linux x86_64 服务器"
    echo "  2. 给文件添加执行权限: chmod +x *_linux"
    echo "  3. 运行管理器: ./self-manage_linux"
    
else
    echo "❌ 编译失败! 成功: $SUCCESS_COUNT/$TOTAL_COUNT"
    exit 1
fi

echo ""
echo "Linux x86_64 交叉编译完成!"
