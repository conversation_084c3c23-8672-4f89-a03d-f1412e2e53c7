package main

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"shared/logger"
	"sync"
	"time"
)

// DeviceConfig 设备配置结构体
// 从API获取的设备配置信息
type DeviceConfig struct {
	ID              string `json:"id"`               // 设备唯一标识符
	Name            string `json:"name"`             // 设备名称
	IP              string `json:"ip"`               // 设备IP地址
	Port            int    `json:"port"`             // 设备端口号
	Brand           string `json:"brand"`            // 设备品牌
	Model           string `json:"model"`            // 设备型号
	Location        string `json:"location"`         // 设备位置
	DataType        string `json:"data_type"`        // 数据类型
	Enabled         bool   `json:"enabled"`          // 是否启用
	AutoStart       bool   `json:"auto_start"`       // 是否自动启动
	CollectInterval int    `json:"collect_interval"` // 数据采集间隔(毫秒)
	Timeout         int    `json:"timeout"`          // 连接超时时间(秒)
	RetryCount      int    `json:"retry_count"`      // 重试次数
	RetryDelay      int    `json:"retry_delay"`      // 重试延迟(秒)
}

// APIResponse API响应结构体
// 从API获取的标准响应格式
type APIResponse struct {
	Success bool           `json:"success"` // 请求是否成功
	Message string         `json:"message"` // 响应消息
	Data    []DeviceConfig `json:"data"`    // 设备配置列表
}

// Process 进程管理结构体，类似supervisord的进程配置
type Process struct {
	Cmd           *exec.Cmd // 当前运行的命令对象
	Name          string    // 进程名称
	Args          []string  // 命令行参数
	IP            string    // 设备IP地址（用于标识）
	DeviceID      string    // 设备ID
	DeviceName    string    // 设备名称
	AutoRestart   bool      // 是否自动重启
	RestartDelay  int       // 重启延迟（秒）
	MaxRestarts   int       // 最大重启次数（0表示无限制）
	RestartCount  int       // 当前重启次数
	StartTime     time.Time // 进程启动时间
	LastExitTime  time.Time // 上次退出时间
	Status        string    // 进程状态：STARTING, RUNNING, STOPPING, STOPPED, FATAL
	StopRequested bool      // 是否请求停止（用于优雅关闭）
}

// ProcessManager 进程管理器
// 管理所有设备进程的生命周期，支持动态添加和移除设备
type ProcessManager struct {
	processes       map[string]*Process // 设备ID -> 进程映射
	wg              *sync.WaitGroup     // 等待组
	mutex           sync.RWMutex        // 读写锁，保护processes映射
	apiURL          string              // API URL
	maxRetries      int                 // 最大重试次数
	timeout         time.Duration       // 请求超时时间
	refreshInterval time.Duration       // 设备列表刷新间隔
	stopChan        chan bool           // 停止信号通道
}

// fetchDeviceConfigs 从API获取设备配置列表
// 支持重试机制和超时处理，确保程序稳健性
func fetchDeviceConfigs(apiURL string, maxRetries int, timeout time.Duration) ([]DeviceConfig, error) {
	logger.Infof("开始从API获取设备配置: %s", apiURL)

	// 创建HTTP客户端，设置超时时间
	client := &http.Client{
		Timeout: timeout,
	}

	var lastErr error

	// 重试机制
	for attempt := 1; attempt <= maxRetries; attempt++ {
		logger.Infof("第 %d 次尝试获取设备配置 (最多 %d 次)", attempt, maxRetries)

		// 发送HTTP GET请求
		resp, err := client.Get(apiURL)
		if err != nil {
			lastErr = fmt.Errorf("HTTP请求失败: %v", err)
			logger.Warnf("第 %d 次请求失败: %v", attempt, err)

			if attempt < maxRetries {
				waitTime := time.Duration(attempt) * 2 * time.Second // 递增等待时间
				logger.Infof("等待 %v 后重试...", waitTime)
				time.Sleep(waitTime)
				continue
			}
			break
		}

		// 确保响应体被关闭
		defer resp.Body.Close()

		// 检查HTTP状态码
		if resp.StatusCode != http.StatusOK {
			lastErr = fmt.Errorf("API返回错误状态码: %d", resp.StatusCode)
			logger.Warnf("第 %d 次请求返回状态码: %d", attempt, resp.StatusCode)

			if attempt < maxRetries {
				waitTime := time.Duration(attempt) * 2 * time.Second
				logger.Infof("等待 %v 后重试...", waitTime)
				time.Sleep(waitTime)
				continue
			}
			break
		}

		// 读取响应体
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			lastErr = fmt.Errorf("读取响应体失败: %v", err)
			logger.Warnf("第 %d 次读取响应体失败: %v", attempt, err)

			if attempt < maxRetries {
				waitTime := time.Duration(attempt) * 2 * time.Second
				logger.Infof("等待 %v 后重试...", waitTime)
				time.Sleep(waitTime)
				continue
			}
			break
		}

		// 解析JSON响应
		var apiResponse APIResponse
		if err := json.Unmarshal(body, &apiResponse); err != nil {
			lastErr = fmt.Errorf("解析JSON响应失败: %v", err)
			logger.Warnf("第 %d 次JSON解析失败: %v", attempt, err)
			logger.Debugf("响应内容: %s", string(body))

			if attempt < maxRetries {
				waitTime := time.Duration(attempt) * 2 * time.Second
				logger.Infof("等待 %v 后重试...", waitTime)
				time.Sleep(waitTime)
				continue
			}
			break
		}

		// 检查API响应是否成功
		if !apiResponse.Success {
			lastErr = fmt.Errorf("API返回失败状态: %s", apiResponse.Message)
			logger.Warnf("第 %d 次API返回失败: %s", attempt, apiResponse.Message)

			if attempt < maxRetries {
				waitTime := time.Duration(attempt) * 2 * time.Second
				logger.Infof("等待 %v 后重试...", waitTime)
				time.Sleep(waitTime)
				continue
			}
			break
		}

		// 检查设备列表是否为空
		if len(apiResponse.Data) == 0 {
			lastErr = fmt.Errorf("API返回空的设备列表")
			logger.Warnf("第 %d 次API返回空设备列表", attempt)

			if attempt < maxRetries {
				waitTime := time.Duration(attempt) * 2 * time.Second
				logger.Infof("等待 %v 后重试...", waitTime)
				time.Sleep(waitTime)
				continue
			}
			break
		}

		// 成功获取设备配置
		logger.Infof("成功获取设备配置，共 %d 个设备", len(apiResponse.Data))
		return apiResponse.Data, nil
	}

	// 所有重试都失败了
	return nil, fmt.Errorf("获取设备配置失败，已重试 %d 次，最后错误: %v", maxRetries, lastErr)
}

// getDefaultDeviceConfigs 获取默认的设备配置列表
// 当API不可用时作为备用方案
func getDefaultDeviceConfigs() []DeviceConfig {
	logger.Warn("使用默认设备为空")
	return []DeviceConfig{}
}

// NewProcessManager 创建新的进程管理器
func NewProcessManager(apiURL string, maxRetries int, timeout time.Duration, refreshInterval time.Duration) *ProcessManager {
	return &ProcessManager{
		processes:       make(map[string]*Process),
		wg:              &sync.WaitGroup{},
		apiURL:          apiURL,
		maxRetries:      maxRetries,
		timeout:         timeout,
		refreshInterval: refreshInterval,
		stopChan:        make(chan bool, 1),
	}
}

// addProcess 添加新进程到管理器
func (pm *ProcessManager) addProcess(deviceConfig DeviceConfig) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	// 检查进程是否已存在
	if _, exists := pm.processes[deviceConfig.ID]; exists {
		logger.Infof("设备 %s (%s) 已存在，跳过添加", deviceConfig.Name, deviceConfig.ID)
		return
	}

	// 创建进程配置
	args := []string{"-ip", deviceConfig.IP}
	process := createProcess("self-process", args, deviceConfig)

	// 启动进程
	if startProcess(process) {
		pm.processes[deviceConfig.ID] = process

		// 启动监控协程
		pm.wg.Add(1)
		go pm.monitorProcessWithManager(process)

		logger.Infof("成功添加新设备进程: %s (%s, IP: %s)",
			deviceConfig.Name, deviceConfig.ID, deviceConfig.IP)
		fmt.Printf("[%s] 成功添加新设备进程: %s (%s, IP: %s)\n",
			time.Now().Format("15:04:05"), deviceConfig.Name, deviceConfig.ID, deviceConfig.IP)
	} else {
		logger.Errorf("启动新设备进程失败: %s (%s, IP: %s)",
			deviceConfig.Name, deviceConfig.ID, deviceConfig.IP)
		fmt.Printf("[%s] 启动新设备进程失败: %s (%s, IP: %s)\n",
			time.Now().Format("15:04:05"), deviceConfig.Name, deviceConfig.ID, deviceConfig.IP)
	}
}

// removeProcess 从管理器中移除进程
func (pm *ProcessManager) removeProcess(deviceID string) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	process, exists := pm.processes[deviceID]
	if !exists {
		logger.Warnf("尝试移除不存在的设备进程: %s", deviceID)
		return
	}

	// 标记进程为停止请求
	process.StopRequested = true

	// 如果进程正在运行，终止它
	if process.Cmd != nil && process.Cmd.Process != nil {
		logger.Infof("正在停止设备进程: %s (%s)", process.DeviceName, deviceID)
		fmt.Printf("[%s] 正在停止设备进程: %s (%s)\n",
			time.Now().Format("15:04:05"), process.DeviceName, deviceID)

		if err := process.Cmd.Process.Kill(); err != nil {
			logger.Errorf("终止进程失败: %v", err)
		}
	}

	// 从映射中删除
	delete(pm.processes, deviceID)

	logger.Infof("已移除设备进程: %s (%s)", process.DeviceName, deviceID)
	fmt.Printf("[%s] 已移除设备进程: %s (%s)\n",
		time.Now().Format("15:04:05"), process.DeviceName, deviceID)
}

// getProcessCount 获取当前进程数量
func (pm *ProcessManager) getProcessCount() int {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()
	return len(pm.processes)
}

// listProcesses 列出所有进程
func (pm *ProcessManager) listProcesses() map[string]*Process {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	// 创建副本以避免并发访问问题
	processes := make(map[string]*Process)
	for id, process := range pm.processes {
		processes[id] = process
	}
	return processes
}

// createProcess 创建进程配置对象
func createProcess(name string, args []string, deviceConfig DeviceConfig) *Process {
	return &Process{
		Name:         name,
		Args:         args,
		IP:           deviceConfig.IP,
		DeviceID:     deviceConfig.ID,
		DeviceName:   deviceConfig.Name,
		AutoRestart:  deviceConfig.AutoStart,  // 使用设备配置的自动启动设置
		RestartDelay: deviceConfig.RetryDelay, // 使用设备配置的重试延迟
		MaxRestarts:  0,                       // 0表示无限制重启
		RestartCount: 0,
		Status:       "STOPPED",
	}
}

// startProcess 启动进程实例
func startProcess(process *Process) bool {
	if process == nil {
		logger.Error("进程对象为空，无法启动")
		return false
	}

	// 构建可执行文件的完整路径
	executablePath := "./" + process.Name

	process.Status = "STARTING"
	logger.Infof("尝试启动进程: %s，参数: %v (IP: %s)", executablePath, process.Args, process.IP)
	fmt.Printf("[%s] 尝试启动进程: %s，参数: %v (IP: %s)\n",
		time.Now().Format("15:04:05"), executablePath, process.Args, process.IP)

	cmd := exec.Command(executablePath, process.Args...)
	if err := cmd.Start(); err != nil {
		process.Status = "FATAL"
		logger.Errorf("启动失败 %s (IP: %s): %v", executablePath, process.IP, err)
		fmt.Printf("[%s] 启动失败 %s (IP: %s): %v\n",
			time.Now().Format("15:04:05"), executablePath, process.IP, err)
		return false
	}

	// 更新进程信息
	process.Cmd = cmd
	process.StartTime = time.Now()
	process.Status = "RUNNING"

	logger.Infof("进程启动成功，PID: %d (IP: %s)", cmd.Process.Pid, process.IP)
	fmt.Printf("[%s] 进程启动成功，PID: %d (IP: %s)\n",
		time.Now().Format("15:04:05"), cmd.Process.Pid, process.IP)
	return true
}

// monitorProcessWithManager 在进程管理器中监控进程状态并自动重启
func (pm *ProcessManager) monitorProcessWithManager(process *Process) {
	defer pm.wg.Done()

	// 检查进程对象是否有效
	if process == nil || process.Cmd == nil {
		logger.Error("进程对象无效，无法监控")
		fmt.Printf("[%s] 进程对象无效，无法监控\n", time.Now().Format("15:04:05"))
		return
	}

	pid := process.Cmd.Process.Pid
	logger.Infof("开始监控进程 PID: %d (设备: %s, IP: %s)", pid, process.DeviceName, process.IP)
	fmt.Printf("[%s] 开始监控进程 PID: %d (设备: %s, IP: %s)\n",
		time.Now().Format("15:04:05"), pid, process.DeviceName, process.IP)

	// 等待进程结束
	err := process.Cmd.Wait()

	// 记录退出时间
	process.LastExitTime = time.Now()
	runDuration := process.LastExitTime.Sub(process.StartTime).Seconds()

	if err != nil {
		process.Status = "STOPPED"
		logger.Warnf("进程 %s (设备: %s, IP: %s, PID: %d) 异常退出: %v，运行时长: %.1f秒",
			process.Name, process.DeviceName, process.IP, pid, err, runDuration)
		fmt.Printf("[%s] 进程 %s (设备: %s, IP: %s, PID: %d) 异常退出: %v，运行时长: %.1f秒\n",
			time.Now().Format("15:04:05"), process.Name, process.DeviceName, process.IP, pid, err, runDuration)
	} else {
		process.Status = "STOPPED"
		logger.Infof("进程 %s (设备: %s, IP: %s, PID: %d) 正常退出，运行时长: %.1f秒",
			process.Name, process.DeviceName, process.IP, pid, runDuration)
		fmt.Printf("[%s] 进程 %s (设备: %s, IP: %s, PID: %d) 正常退出，运行时长: %.1f秒\n",
			time.Now().Format("15:04:05"), process.Name, process.DeviceName, process.IP, pid, runDuration)
	}

	// 检查是否请求停止
	if process.StopRequested {
		logger.Infof("进程 %s (设备: %s) 已请求停止，不再重启", process.Name, process.DeviceName)
		fmt.Printf("[%s] 进程 %s (设备: %s) 已请求停止，不再重启\n",
			time.Now().Format("15:04:05"), process.Name, process.DeviceName)
		return
	}

	// 检查是否需要自动重启
	if process.AutoRestart {
		// 检查最大重启次数限制
		if process.MaxRestarts > 0 && process.RestartCount >= process.MaxRestarts {
			process.Status = "FATAL"
			logger.Errorf("进程 %s (设备: %s) 已达到最大重启次数 %d，不再重启",
				process.Name, process.DeviceName, process.MaxRestarts)
			fmt.Printf("[%s] 进程 %s (设备: %s) 已达到最大重启次数 %d，不再重启\n",
				time.Now().Format("15:04:05"), process.Name, process.DeviceName, process.MaxRestarts)
			return
		}

		// 等待指定的重启延迟时间
		if process.RestartDelay > 0 {
			logger.Infof("等待 %d 秒后重启进程 %s (设备: %s)",
				process.RestartDelay, process.Name, process.DeviceName)
			fmt.Printf("[%s] 等待 %d 秒后重启进程 %s (设备: %s)...\n",
				time.Now().Format("15:04:05"), process.RestartDelay, process.Name, process.DeviceName)
			time.Sleep(time.Duration(process.RestartDelay) * time.Second)
		}

		// 增加重启计数
		process.RestartCount++

		// 重启进程
		if startProcess(process) {
			logger.Infof("进程 %s (设备: %s) 重启成功，这是第 %d 次重启",
				process.Name, process.DeviceName, process.RestartCount)
			fmt.Printf("[%s] 进程 %s (设备: %s) 重启成功，这是第 %d 次重启\n",
				time.Now().Format("15:04:05"), process.Name, process.DeviceName, process.RestartCount)

			// 递归监控新启动的进程
			pm.wg.Add(1)
			go pm.monitorProcessWithManager(process)
		} else {
			process.Status = "FATAL"
			logger.Errorf("进程 %s (设备: %s) 重启失败，放弃监控", process.Name, process.DeviceName)
			fmt.Printf("[%s] 进程 %s (设备: %s) 重启失败，放弃监控\n",
				time.Now().Format("15:04:05"), process.Name, process.DeviceName)
		}
	} else {
		logger.Infof("进程 %s (设备: %s) 已禁用自动重启，不再重启", process.Name, process.DeviceName)
		fmt.Printf("[%s] 进程 %s (设备: %s) 已禁用自动重启，不再重启\n",
			time.Now().Format("15:04:05"), process.Name, process.DeviceName)
	}
}

// monitorProcess 监控进程状态并自动重启
// 实现类似supervisord的进程监控和自动重启功能
func monitorProcess(process *Process, wg *sync.WaitGroup) {
	defer wg.Done()

	// 检查进程对象是否有效
	if process == nil || process.Cmd == nil {
		logger.Error("进程对象无效，无法监控")
		fmt.Printf("[%s] 进程对象无效，无法监控\n", time.Now().Format("15:04:05"))
		return
	}

	pid := process.Cmd.Process.Pid
	logger.Infof("开始监控进程 PID: %d (IP: %s)", pid, process.IP)
	fmt.Printf("[%s] 开始监控进程 PID: %d (IP: %s)\n",
		time.Now().Format("15:04:05"), pid, process.IP)

	// 等待进程结束
	err := process.Cmd.Wait()

	// 记录退出时间
	process.LastExitTime = time.Now()
	runDuration := process.LastExitTime.Sub(process.StartTime).Seconds()

	if err != nil {
		process.Status = "STOPPED"
		logger.Warnf("进程 %s (IP: %s, PID: %d) 异常退出: %v，运行时长: %.1f秒",
			process.Name, process.IP, pid, err, runDuration)
		fmt.Printf("[%s] 进程 %s (IP: %s, PID: %d) 异常退出: %v，运行时长: %.1f秒\n",
			time.Now().Format("15:04:05"), process.Name, process.IP, pid, err, runDuration)
	} else {
		process.Status = "STOPPED"
		logger.Infof("进程 %s (IP: %s, PID: %d) 正常退出，运行时长: %.1f秒",
			process.Name, process.IP, pid, runDuration)
		fmt.Printf("[%s] 进程 %s (IP: %s, PID: %d) 正常退出，运行时长: %.1f秒\n",
			time.Now().Format("15:04:05"), process.Name, process.IP, pid, runDuration)
	}

	// 检查是否需要自动重启
	if process.AutoRestart {
		// 检查最大重启次数限制
		if process.MaxRestarts > 0 && process.RestartCount >= process.MaxRestarts {
			process.Status = "FATAL"
			logger.Errorf("进程 %s (IP: %s) 已达到最大重启次数 %d，不再重启",
				process.Name, process.IP, process.MaxRestarts)
			fmt.Printf("[%s] 进程 %s (IP: %s) 已达到最大重启次数 %d，不再重启\n",
				time.Now().Format("15:04:05"), process.Name, process.IP, process.MaxRestarts)
			return
		}

		// 等待指定的重启延迟时间
		if process.RestartDelay > 0 {
			logger.Infof("等待 %d 秒后重启进程 %s (IP: %s)",
				process.RestartDelay, process.Name, process.IP)
			fmt.Printf("[%s] 等待 %d 秒后重启进程 %s (IP: %s)...\n",
				time.Now().Format("15:04:05"), process.RestartDelay, process.Name, process.IP)
			time.Sleep(time.Duration(process.RestartDelay) * time.Second)
		}

		// 增加重启计数
		process.RestartCount++

		// 重启进程
		if startProcess(process) {
			logger.Infof("进程 %s (IP: %s) 重启成功，这是第 %d 次重启",
				process.Name, process.IP, process.RestartCount)
			fmt.Printf("[%s] 进程 %s (IP: %s) 重启成功，这是第 %d 次重启\n",
				time.Now().Format("15:04:05"), process.Name, process.IP, process.RestartCount)

			// 递归监控新启动的进程
			wg.Add(1)
			go monitorProcess(process, wg)
		} else {
			process.Status = "FATAL"
			logger.Errorf("进程 %s (IP: %s) 重启失败，放弃监控", process.Name, process.IP)
			fmt.Printf("[%s] 进程 %s (IP: %s) 重启失败，放弃监控\n",
				time.Now().Format("15:04:05"), process.Name, process.IP)
		}
	} else {
		logger.Infof("进程 %s (IP: %s) 已禁用自动重启，不再重启", process.Name, process.IP)
		fmt.Printf("[%s] 进程 %s (IP: %s) 已禁用自动重启，不再重启\n",
			time.Now().Format("15:04:05"), process.Name, process.IP)
	}
}

// refreshDeviceList 定时刷新设备列表
func (pm *ProcessManager) refreshDeviceList() {
	logger.Info("启动设备列表定时刷新服务")
	fmt.Printf("[%s] 启动设备列表定时刷新服务，刷新间隔: %v\n",
		time.Now().Format("15:04:05"), pm.refreshInterval)

	ticker := time.NewTicker(pm.refreshInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			pm.checkAndUpdateDevices()
		case <-pm.stopChan:
			logger.Info("设备列表刷新服务已停止")
			fmt.Printf("[%s] 设备列表刷新服务已停止\n", time.Now().Format("15:04:05"))
			return
		}
	}
}

// checkAndUpdateDevices 检查并更新设备列表
func (pm *ProcessManager) checkAndUpdateDevices() {
	logger.Info("开始检查设备列表更新")
	fmt.Printf("[%s] 开始检查设备列表更新\n", time.Now().Format("15:04:05"))

	// 从API获取最新的设备配置
	deviceConfigs, err := fetchDeviceConfigs(pm.apiURL, pm.maxRetries, pm.timeout)
	if err != nil {
		logger.Errorf("定时刷新设备配置失败: %v", err)
		fmt.Printf("[%s] 定时刷新设备配置失败: %v\n", time.Now().Format("15:04:05"), err)
		return
	}

	// 过滤启用且自动启动的设备
	var enabledDevices []DeviceConfig
	for _, device := range deviceConfigs {
		if device.Enabled && device.AutoStart {
			enabledDevices = append(enabledDevices, device)
		}
	}

	logger.Infof("从API获取到 %d 个启用且自动启动的设备", len(enabledDevices))

	// 获取当前进程列表
	currentProcesses := pm.listProcesses()

	// 检查新设备
	newDevicesCount := 0
	for _, deviceConfig := range enabledDevices {
		if _, exists := currentProcesses[deviceConfig.ID]; !exists {
			logger.Infof("发现新设备: %s (%s, IP: %s)",
				deviceConfig.Name, deviceConfig.ID, deviceConfig.IP)
			fmt.Printf("[%s] 发现新设备: %s (%s, IP: %s)\n",
				time.Now().Format("15:04:05"), deviceConfig.Name, deviceConfig.ID, deviceConfig.IP)

			// 添加新设备进程
			pm.addProcess(deviceConfig)
			newDevicesCount++
		}
	}

	// 检查已移除的设备
	removedDevicesCount := 0
	apiDeviceIDs := make(map[string]bool)
	for _, device := range enabledDevices {
		apiDeviceIDs[device.ID] = true
	}

	for deviceID, process := range currentProcesses {
		if !apiDeviceIDs[deviceID] {
			logger.Infof("设备已从API中移除: %s (%s)", process.DeviceName, deviceID)
			fmt.Printf("[%s] 设备已从API中移除: %s (%s)\n",
				time.Now().Format("15:04:05"), process.DeviceName, deviceID)

			// 移除设备进程
			pm.removeProcess(deviceID)
			removedDevicesCount++
		}
	}

	// 汇总更新结果
	if newDevicesCount > 0 || removedDevicesCount > 0 {
		logger.Infof("设备列表更新完成: 新增 %d 个设备, 移除 %d 个设备, 当前总数: %d",
			newDevicesCount, removedDevicesCount, pm.getProcessCount())
		fmt.Printf("[%s] 设备列表更新完成: 新增 %d 个设备, 移除 %d 个设备, 当前总数: %d\n",
			time.Now().Format("15:04:05"), newDevicesCount, removedDevicesCount, pm.getProcessCount())
	} else {
		logger.Info("设备列表无变化")
		fmt.Printf("[%s] 设备列表无变化，当前设备数: %d\n",
			time.Now().Format("15:04:05"), pm.getProcessCount())
	}
}

// stop 停止进程管理器
func (pm *ProcessManager) stop() {
	logger.Info("正在停止进程管理器...")
	fmt.Printf("[%s] 正在停止进程管理器...\n", time.Now().Format("15:04:05"))

	// 发送停止信号
	select {
	case pm.stopChan <- true:
	default:
	}

	// 停止所有进程
	processes := pm.listProcesses()
	for deviceID := range processes {
		pm.removeProcess(deviceID)
	}

	// 等待所有监控协程结束
	pm.wg.Wait()

	logger.Info("进程管理器已停止")
	fmt.Printf("[%s] 进程管理器已停止\n", time.Now().Format("15:04:05"))
}

func main() {
	// 初始化日志系统
	// 创建日志目录
	logDir := "logs"
	if err := os.MkdirAll(logDir, 0755); err != nil {
		fmt.Printf("创建日志目录失败: %v\n", err)
		os.Exit(1)
	}

	// 获取当前时间用于日志文件命名
	now := time.Now()
	dateStr := now.Format("2006-01-02")
	logFile := filepath.Join(logDir, fmt.Sprintf("self-manage_%s.log", dateStr))

	// 配置日志轮转参数
	rotation := logger.LogRotationConfig{
		Enabled:    true, // 启用日志轮转
		MaxSize:    20,   // 单个文件最大20MB
		MaxAge:     30,   // 保留30天
		MaxBackups: 10,   // 保留10个备份文件
		Compress:   true, // 压缩旧文件
	}

	// 初始化日志系统：info级别，文本格式，输出到文件，启用轮转
	logger.InitLoggerWithRotation("info", "text", logFile, rotation)

	// 记录程序启动信息
	logger.Info("========== 进程管理器启动 ==========")
	logger.Info("类似supervisord的自动重启功能已启用")
	logger.Info("支持动态设备列表刷新和新设备自动添加")
	logger.Infof("日志文件路径: %s", logFile)

	fmt.Printf("[%s] ========== 进程管理器启动 ==========\n", time.Now().Format("15:04:05"))
	fmt.Printf("[%s] 类似supervisord的自动重启功能已启用\n", time.Now().Format("15:04:05"))
	fmt.Printf("[%s] 支持动态设备列表刷新和新设备自动添加\n", time.Now().Format("15:04:05"))
	fmt.Printf("[%s] 日志文件: %s\n", time.Now().Format("15:04:05"), logFile)

	// API配置
	apiURL := "http://localhost:9105/api/public/devices/configs"
	maxRetries := 3
	timeout := 10 * time.Second
	refreshInterval := 30 * time.Second // 每30秒刷新一次设备列表

	logger.Infof("API配置: URL=%s, 最大重试=%d, 超时=%v, 刷新间隔=%v",
		apiURL, maxRetries, timeout, refreshInterval)
	fmt.Printf("[%s] API配置: %s (刷新间隔: %v)\n",
		time.Now().Format("15:04:05"), apiURL, refreshInterval)

	// 创建进程管理器
	processManager := NewProcessManager(apiURL, maxRetries, timeout, refreshInterval)

	// 初始化设备列表
	fmt.Printf("[%s] 初始化设备列表...\n", time.Now().Format("15:04:05"))
	processManager.checkAndUpdateDevices()

	// 检查是否有设备启动
	if processManager.getProcessCount() == 0 {
		logger.Warn("没有设备成功启动，尝试使用默认配置")
		fmt.Printf("[%s] 没有设备成功启动，尝试使用默认配置\n", time.Now().Format("15:04:05"))

		// 使用默认配置
		defaultDevices := getDefaultDeviceConfigs()
		for _, deviceConfig := range defaultDevices {
			if deviceConfig.Enabled && deviceConfig.AutoStart {
				processManager.addProcess(deviceConfig)
			}
		}

		if processManager.getProcessCount() == 0 {
			logger.Fatal("没有进程成功启动，程序退出")
			fmt.Printf("[%s] 没有进程成功启动，程序退出\n", time.Now().Format("15:04:05"))
			return
		}
	}

	logger.Infof("初始化完成，当前管理 %d 个设备进程", processManager.getProcessCount())
	fmt.Printf("[%s] 初始化完成，当前管理 %d 个设备进程\n",
		time.Now().Format("15:04:05"), processManager.getProcessCount())

	// 启动设备列表定时刷新服务
	go processManager.refreshDeviceList()

	logger.Info("所有服务已启动，等待进程结束...")
	fmt.Printf("[%s] 所有服务已启动，等待进程结束...\n", time.Now().Format("15:04:05"))
	fmt.Printf("[%s] 设备列表将每 %v 自动刷新\n", time.Now().Format("15:04:05"), refreshInterval)
	fmt.Printf("[%s] 按 Ctrl+C 可以停止管理器\n", time.Now().Format("15:04:05"))

	// 等待所有进程结束
	processManager.wg.Wait()
	logger.Info("所有进程已结束，程序退出")
	fmt.Printf("[%s] 所有进程已结束，程序退出\n", time.Now().Format("15:04:05"))
}
