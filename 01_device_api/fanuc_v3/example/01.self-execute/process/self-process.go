package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"time"

	"shared/logger"
)

// Device 设备信息结构体
// 定义FANUC设备的完整配置信息
type Device struct {
	ID              string `json:"id"`               // 设备唯一标识符
	Name            string `json:"name"`             // 设备名称
	IP              string `json:"ip"`               // 设备IP地址
	Port            int    `json:"port"`             // 设备端口号
	Brand           string `json:"brand"`            // 设备品牌
	Model           string `json:"model"`            // 设备型号
	Location        string `json:"location"`         // 设备位置
	DataType        string `json:"data_type"`        // 数据类型
	Enabled         bool   `json:"enabled"`          // 是否启用
	AutoStart       bool   `json:"auto_start"`       // 是否自动启动
	CollectInterval int    `json:"collect_interval"` // 数据采集间隔(毫秒)
	Timeout         int    `json:"timeout"`          // 连接超时时间(秒)
	RetryCount      int    `json:"retry_count"`      // 重试次数
	RetryDelay      int    `json:"retry_delay"`      // 重试延迟(秒)
}

// APIResponse 统一的API响应结构体
// 提供标准化的响应格式，包含成功状态、消息和数据
type APIResponse struct {
	Success bool        `json:"success"` // 请求是否成功
	Message string      `json:"message"` // 响应消息
	Data    interface{} `json:"data"`    // 响应数据
}

// checkDeviceExists 检查设备是否存在
// 通过API调用检查指定ID的设备是否存在于系统中
// 参数: deviceID - 设备ID, apiURL - API服务器地址
// 返回: 设备是否存在(bool), 设备信息(Device), 错误信息(error)
func checkDeviceExists(deviceID, apiURL string) (bool, Device, error) {
	// 构建完整的API请求URL
	// 使用设备ID作为路径参数调用获取设备详情接口
	url := fmt.Sprintf("%s/api/public/device/%s", apiURL, deviceID)

	logger.Debugf("正在检查设备是否存在: %s", url)

	// 创建HTTP GET请求
	// 设置5秒超时时间，避免长时间等待
	client := &http.Client{
		Timeout: 5 * time.Second,
	}

	// 发送GET请求获取设备信息
	resp, err := client.Get(url)
	if err != nil {
		logger.Errorf("API请求失败: %v", err)
		return false, Device{}, fmt.Errorf("API请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应体内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Errorf("读取响应失败: %v", err)
		return false, Device{}, fmt.Errorf("读取响应失败: %v", err)
	}

	// 解析JSON响应
	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		logger.Errorf("解析JSON响应失败: %v", err)
		return false, Device{}, fmt.Errorf("解析JSON响应失败: %v", err)
	}

	// 检查API响应状态
	if !apiResp.Success {
		// 设备不存在或其他错误
		logger.Warnf("设备不存在或API返回错误: %s", apiResp.Message)
		return false, Device{}, nil
	}

	// 解析设备数据
	deviceData, err := json.Marshal(apiResp.Data)
	if err != nil {
		logger.Errorf("序列化设备数据失败: %v", err)
		return false, Device{}, fmt.Errorf("序列化设备数据失败: %v", err)
	}

	var device Device
	if err := json.Unmarshal(deviceData, &device); err != nil {
		logger.Errorf("解析设备数据失败: %v", err)
		return false, Device{}, fmt.Errorf("解析设备数据失败: %v", err)
	}

	logger.Infof("设备存在: ID=%s, Name=%s, IP=%s", device.ID, device.Name, device.IP)
	return true, device, nil
}

func main() {

	// 接收命令行参数 -ip 和 -id
	// 定义ip参数，默认值为"127.0.0.1"，用法说明
	ipPtr := flag.String("ip", "127.0.0.1", "设备IP地址")
	// 定义id参数，用于指定设备ID，必填参数
	idPtr := flag.String("id", "", "设备ID（必填）")
	// 定义API服务器地址参数，如果未提供则从环境变量获取
	apiPtr := flag.String("api", "", "API服务器地址（可从环境变量API_URL获取）")
	// 定义检查间隔参数，如果未提供则从环境变量获取
	intervalPtr := flag.Int("interval", 0, "设备存在性检查间隔（秒）（可从环境变量CHECK_INTERVAL获取）")

	// 解析命令行参数
	flag.Parse()

	// 初始化统一的日志系统
	// 使用shared/logger包，支持日志轮转和多种输出格式
	logDir := "logs"
	if err := os.MkdirAll(logDir, 0755); err != nil {
		fmt.Printf("创建日志目录失败: %v\n", err)
		os.Exit(1)
	}

	device_flag := "default"

	if len(*ipPtr) > 0 {
		device_flag = *ipPtr
	} else if len(*idPtr) > 0 {
		device_flag = *idPtr
	}

	// 获取当前时间用于日志文件命名
	now := time.Now()
	dateStr := now.Format("2006-01-02")
	// 使用设备ID作为日志文件名的一部分，便于区分不同设备的日志
	logFile := filepath.Join(logDir, fmt.Sprintf("self-process_%s-%s.log", dateStr, device_flag))

	// 配置日志轮转参数
	rotation := logger.LogRotationConfig{
		Enabled:    true, // 启用日志轮转
		MaxSize:    10,   // 单个文件最大10MB
		MaxAge:     7,    // 保留7天
		MaxBackups: 5,    // 保留5个备份文件
		Compress:   true, // 压缩旧文件
	}

	// 初始化日志系统：info级别，文本格式，输出到文件，启用轮转
	logger.InitLoggerWithRotation("info", "text", logFile, rotation)

	// 处理API服务器地址：优先使用命令行参数，其次环境变量，最后默认值
	apiURL := *apiPtr
	var apiSource string
	if apiURL == "" {
		// 从环境变量获取API地址
		if envAPI := os.Getenv("PROCESSS_API_URL"); envAPI != "" {
			apiURL = envAPI
			apiSource = "环境变量API_URL"
		} else {
			// 使用默认值
			apiURL = "http://localhost:9105"
			apiSource = "默认值"
		}
	} else {
		apiSource = "命令行参数"
	}

	// 处理检查间隔：优先使用命令行参数，其次环境变量，最后默认值
	checkInterval := *intervalPtr
	var intervalSource string
	if checkInterval == 0 {
		// 从环境变量获取检查间隔
		if envInterval := os.Getenv("CHECK_INTERVAL"); envInterval != "" {
			if interval, err := strconv.Atoi(envInterval); err == nil && interval > 0 {
				checkInterval = interval
				intervalSource = "环境变量CHECK_INTERVAL"
			} else {
				fmt.Printf("警告: 环境变量CHECK_INTERVAL值无效: %s，使用默认值30秒\n", envInterval)
				checkInterval = 30
				intervalSource = "默认值(环境变量无效)"
			}
		} else {
			// 使用默认值
			checkInterval = 30
			intervalSource = "默认值"
		}
	} else {
		intervalSource = "命令行参数"
	}

	// 记录程序启动信息
	logger.Infof("程序启动，设备ID: %s, IP: %s", *idPtr, *ipPtr)
	logger.Infof("API服务器: %s (来源: %s)", apiURL, apiSource)
	logger.Infof("检查间隔: %d秒 (来源: %s)", checkInterval, intervalSource)
	logger.Infof("日志文件路径: %s", logFile)

	// 验证必填参数
	if *idPtr == "" {
		logger.Info("错误: 设备ID参数(-id)是必填的")
		logger.Info("使用方法: ./self-process -id=设备ID [-ip=设备IP] [-api=API地址] [-interval=检查间隔秒数]")
		logger.Info("环境变量: API_URL=API服务器地址, CHECK_INTERVAL=检查间隔秒数")
		logger.Info("示例: ./self-process -id=6a1d3fe8-8d21-416g-b9g5-6e8b2290b86f -ip=************")
		logger.Info("示例: API_URL=http://localhost:9105 CHECK_INTERVAL=60 ./self-process -id=设备ID")
		os.Exit(1)
	}

	logger.Infof("设备ID: %s\n", *idPtr)
	logger.Infof("设备IP: %s\n", *ipPtr)
	logger.Infof("API服务器: %s (来源: %s)\n", apiURL, apiSource)
	logger.Infof("检查间隔: %d秒 (来源: %s)\n", checkInterval, intervalSource)
	logger.Infof("日志文件: %s\n", logFile)

	// 初始检查设备是否存在
	logger.Info("开始初始设备存在性检查...")
	exists, device, err := checkDeviceExists(*idPtr, apiURL)
	if err != nil {
		logger.Errorf("初始设备检查失败: %v", err)
		fmt.Printf("初始设备检查失败: %v\n", err)
		os.Exit(1)
	}

	if !exists {
		logger.Errorf("设备不存在，程序退出: ID=%s", *idPtr)
		fmt.Printf("设备不存在，程序退出: ID=%s\n", *idPtr)
		os.Exit(1)
	}

	logger.Infof("设备存在检查通过: ID=%s, Name=%s, IP=%s", device.ID, device.Name, device.IP)
	fmt.Printf("设备存在检查通过: %s (%s)\n", device.Name, device.IP)

	// 创建随机数生成器，使用当前时间作为种子
	// 从Go 1.20开始，推荐使用rand.New()而不是全局的rand.Seed()
	rng := rand.New(rand.NewSource(time.Now().UnixNano()))
	logger.Info("随机数生成器初始化完成")

	// 获取一个 50 至 100 的随机数
	// rng.Intn(51) 生成 0-50 的随机数，加上 50 得到 50-100 的范围
	randomNum := rng.Intn(51) + 50

	// 记录生成的随机数信息
	logger.Infof("生成的随机数: %d，将循环 %d 次", randomNum, randomNum)
	fmt.Printf("生成的随机数: %d，将循环 %d 次\n", randomNum, randomNum)

	// 记录循环开始
	logger.Infof("开始循环处理，设备: %s (%s)", device.Name, device.IP)

	// 创建定时器，用于定期检查设备是否存在
	checkTicker := time.NewTicker(time.Duration(checkInterval) * time.Second)
	defer checkTicker.Stop()

	// 循环这个随机数次
	for i := 1; i <= randomNum; i++ {
		// 检查是否需要进行设备存在性检查
		select {
		case <-checkTicker.C:
			logger.Infof("执行定时设备存在性检查 (间隔: %d秒)", checkInterval)
			exists, currentDevice, err := checkDeviceExists(*idPtr, apiURL)

			if err != nil {
				logger.Errorf("设备检查失败: %v", err)
				// 检查失败不退出，继续运行，可能是网络临时问题
			} else if !exists {
				logger.Errorf("设备不存在，程序退出: ID=%s", *idPtr)
				fmt.Printf("设备不存在，程序退出: ID=%s\n", *idPtr)
				os.Exit(0) // 正常退出，因为设备被删除
			} else {
				logger.Infof("设备存在性检查通过: %s (%s)", currentDevice.Name, currentDevice.IP)
			}
		default:
			// 不阻塞，继续执行主循环
		}

		// 记录每次循环的详细信息
		logger.Debugf("第 %d 次循环，随机数: %d，设备: %s (%s)", i, randomNum, device.Name, device.IP)

		// 每10次循环记录一次info级别日志，避免日志过多
		if i%10 == 0 || i == 1 || i == randomNum {
			logger.Infof("循环进度: %d/%d，设备: %s (%s)", i, randomNum, device.Name, device.IP)
			fmt.Printf("第 %d 次循环，随机数: %d，设备: %s (%s)\n", i, randomNum, device.Name, device.IP)
		}

		// Sleep 1 秒
		time.Sleep(1 * time.Second)

		// 模拟中间检查点（演示警告日志功能）
		if i == randomNum/2 {
			logger.Warnf("到达中间检查点，当前进度: %d/%d，设备: %s (%s)", i, randomNum, device.Name, device.IP)
		}
	}

	// 记录程序完成信息
	logger.Infof("循环完成！总共执行了 %d 次循环，设备: %s (%s)", randomNum, device.Name, device.IP)
	fmt.Printf("循环完成！总共执行了 %d 次循环\n", randomNum)
}
