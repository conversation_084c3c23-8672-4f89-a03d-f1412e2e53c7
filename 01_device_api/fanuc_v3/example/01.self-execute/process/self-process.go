package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"os"
	"path/filepath"
	"time"

	"shared/logger"
)

// Device 设备信息结构体
// 定义FANUC设备的完整配置信息
type Device struct {
	ID              string `json:"id"`               // 设备唯一标识符
	Name            string `json:"name"`             // 设备名称
	IP              string `json:"ip"`               // 设备IP地址
	Port            int    `json:"port"`             // 设备端口号
	Brand           string `json:"brand"`            // 设备品牌
	Model           string `json:"model"`            // 设备型号
	Location        string `json:"location"`         // 设备位置
	DataType        string `json:"data_type"`        // 数据类型
	Enabled         bool   `json:"enabled"`          // 是否启用
	AutoStart       bool   `json:"auto_start"`       // 是否自动启动
	CollectInterval int    `json:"collect_interval"` // 数据采集间隔(毫秒)
	Timeout         int    `json:"timeout"`          // 连接超时时间(秒)
	RetryCount      int    `json:"retry_count"`      // 重试次数
	RetryDelay      int    `json:"retry_delay"`      // 重试延迟(秒)
}

// APIResponse 统一的API响应结构体
// 提供标准化的响应格式，包含成功状态、消息和数据
type APIResponse struct {
	Success bool        `json:"success"` // 请求是否成功
	Message string      `json:"message"` // 响应消息
	Data    interface{} `json:"data"`    // 响应数据
}

// checkDeviceExists 检查设备是否存在
// 通过API调用检查指定ID的设备是否存在于系统中
// 参数: deviceID - 设备ID, apiURL - API服务器地址
// 返回: 设备是否存在(bool), 设备信息(Device), 错误信息(error)
func checkDeviceExists(deviceID, apiURL string) (bool, Device, error) {
	// 构建完整的API请求URL
	// 使用设备ID作为路径参数调用获取设备详情接口
	url := fmt.Sprintf("%s/api/public/device/%s", apiURL, deviceID)

	logger.Debugf("正在检查设备是否存在: %s", url)

	// 创建HTTP GET请求
	// 设置5秒超时时间，避免长时间等待
	client := &http.Client{
		Timeout: 5 * time.Second,
	}

	// 发送GET请求获取设备信息
	resp, err := client.Get(url)
	if err != nil {
		logger.Errorf("API请求失败: %v", err)
		return false, Device{}, fmt.Errorf("API请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应体内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Errorf("读取响应失败: %v", err)
		return false, Device{}, fmt.Errorf("读取响应失败: %v", err)
	}

	// 解析JSON响应
	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		logger.Errorf("解析JSON响应失败: %v", err)
		return false, Device{}, fmt.Errorf("解析JSON响应失败: %v", err)
	}

	// 检查API响应状态
	if !apiResp.Success {
		// 设备不存在或其他错误
		logger.Warnf("设备不存在或API返回错误: %s", apiResp.Message)
		return false, Device{}, nil
	}

	// 解析设备数据
	deviceData, err := json.Marshal(apiResp.Data)
	if err != nil {
		logger.Errorf("序列化设备数据失败: %v", err)
		return false, Device{}, fmt.Errorf("序列化设备数据失败: %v", err)
	}

	var device Device
	if err := json.Unmarshal(deviceData, &device); err != nil {
		logger.Errorf("解析设备数据失败: %v", err)
		return false, Device{}, fmt.Errorf("解析设备数据失败: %v", err)
	}

	logger.Infof("设备存在: ID=%s, Name=%s, IP=%s", device.ID, device.Name, device.IP)
	return true, device, nil
}

func main() {
	// 接收命令行参数 -ip 和 -id
	// 定义ip参数，默认值为"127.0.0.1"，用法说明
	ipPtr := flag.String("ip", "127.0.0.1", "设备IP地址")
	// 定义id参数，用于指定设备ID，必填参数
	idPtr := flag.String("id", "", "设备ID（必填）")
	// 定义API服务器地址参数，默认为本地9105端口
	apiPtr := flag.String("api", "http://localhost:9105", "API服务器地址")
	// 定义检查间隔参数，默认30秒检查一次设备是否存在
	intervalPtr := flag.Int("interval", 30, "设备存在性检查间隔（秒）")

	// 解析命令行参数
	flag.Parse()

	// 验证必填参数
	if *idPtr == "" {
		fmt.Println("错误: 设备ID参数(-id)是必填的")
		fmt.Println("使用方法: ./self-process -id=设备ID [-ip=设备IP] [-api=API地址] [-interval=检查间隔秒数]")
		fmt.Println("示例: ./self-process -id=6a1d3fe8-8d21-416g-b9g5-6e8b2290b86f -ip=************")
		os.Exit(1)
	}

	// 初始化统一的日志系统
	// 使用shared/logger包，支持日志轮转和多种输出格式
	logDir := "logs"
	if err := os.MkdirAll(logDir, 0755); err != nil {
		fmt.Printf("创建日志目录失败: %v\n", err)
		os.Exit(1)
	}

	// 获取当前时间用于日志文件命名
	now := time.Now()
	dateStr := now.Format("2006-01-02")
	// 使用设备ID作为日志文件名的一部分，便于区分不同设备的日志
	logFile := filepath.Join(logDir, fmt.Sprintf("self-process_%s-%s.log", dateStr, *idPtr))

	// 配置日志轮转参数
	rotation := logger.LogRotationConfig{
		Enabled:    true, // 启用日志轮转
		MaxSize:    10,   // 单个文件最大10MB
		MaxAge:     7,    // 保留7天
		MaxBackups: 5,    // 保留5个备份文件
		Compress:   true, // 压缩旧文件
	}

	// 初始化日志系统：info级别，文本格式，输出到文件，启用轮转
	logger.InitLoggerWithRotation("info", "text", logFile, rotation)

	// 记录程序启动信息
	logger.Infof("程序启动，设备ID: %s, IP: %s", *idPtr, *ipPtr)
	logger.Infof("API服务器: %s", *apiPtr)
	logger.Infof("检查间隔: %d秒", *intervalPtr)
	logger.Infof("日志文件路径: %s", logFile)
	fmt.Printf("设备ID: %s\n", *idPtr)
	fmt.Printf("设备IP: %s\n", *ipPtr)
	fmt.Printf("API服务器: %s\n", *apiPtr)
	fmt.Printf("检查间隔: %d秒\n", *intervalPtr)
	fmt.Printf("日志文件: %s\n", logFile)

	// 初始检查设备是否存在
	logger.Info("开始初始设备存在性检查...")
	exists, device, err := checkDeviceExists(*idPtr, *apiPtr)
	if err != nil {
		logger.Errorf("初始设备检查失败: %v", err)
		fmt.Printf("初始设备检查失败: %v\n", err)
		os.Exit(1)
	}

	if !exists {
		logger.Errorf("设备不存在，程序退出: ID=%s", *idPtr)
		fmt.Printf("设备不存在，程序退出: ID=%s\n", *idPtr)
		os.Exit(1)
	}

	logger.Infof("设备存在检查通过: ID=%s, Name=%s, IP=%s", device.ID, device.Name, device.IP)
	fmt.Printf("设备存在检查通过: %s (%s)\n", device.Name, device.IP)

	// 创建随机数生成器，使用当前时间作为种子
	// 从Go 1.20开始，推荐使用rand.New()而不是全局的rand.Seed()
	rng := rand.New(rand.NewSource(time.Now().UnixNano()))
	logger.Info("随机数生成器初始化完成")

	// 获取一个 50 至 100 的随机数
	// rng.Intn(51) 生成 0-50 的随机数，加上 50 得到 50-100 的范围
	randomNum := rng.Intn(51) + 50

	// 记录生成的随机数信息
	logger.Infof("生成的随机数: %d，将循环 %d 次", randomNum, randomNum)
	fmt.Printf("生成的随机数: %d，将循环 %d 次\n", randomNum, randomNum)

	// 记录循环开始
	logger.Infof("开始循环处理，设备: %s (%s)", device.Name, device.IP)

	// 创建定时器，用于定期检查设备是否存在
	checkTicker := time.NewTicker(time.Duration(*intervalPtr) * time.Second)
	defer checkTicker.Stop()

	// 循环这个随机数次
	for i := 1; i <= randomNum; i++ {
		// 检查是否需要进行设备存在性检查
		select {
		case <-checkTicker.C:
			logger.Infof("执行定时设备存在性检查 (间隔: %d秒)", *intervalPtr)
			exists, currentDevice, err := checkDeviceExists(*idPtr, *apiPtr)

			if err != nil {
				logger.Errorf("设备检查失败: %v", err)
				// 检查失败不退出，继续运行，可能是网络临时问题
			} else if !exists {
				logger.Errorf("设备不存在，程序退出: ID=%s", *idPtr)
				fmt.Printf("设备不存在，程序退出: ID=%s\n", *idPtr)
				os.Exit(0) // 正常退出，因为设备被删除
			} else {
				logger.Infof("设备存在性检查通过: %s (%s)", currentDevice.Name, currentDevice.IP)
			}
		default:
			// 不阻塞，继续执行主循环
		}

		// 记录每次循环的详细信息
		logger.Debugf("第 %d 次循环，随机数: %d，设备: %s (%s)", i, randomNum, device.Name, device.IP)

		// 每10次循环记录一次info级别日志，避免日志过多
		if i%10 == 0 || i == 1 || i == randomNum {
			logger.Infof("循环进度: %d/%d，设备: %s (%s)", i, randomNum, device.Name, device.IP)
			fmt.Printf("第 %d 次循环，随机数: %d，设备: %s (%s)\n", i, randomNum, device.Name, device.IP)
		}

		// Sleep 1 秒
		time.Sleep(1 * time.Second)

		// 模拟中间检查点（演示警告日志功能）
		if i == randomNum/2 {
			logger.Warnf("到达中间检查点，当前进度: %d/%d，设备: %s (%s)", i, randomNum, device.Name, device.IP)
		}
	}

	// 记录程序完成信息
	logger.Infof("循环完成！总共执行了 %d 次循环，设备: %s (%s)", randomNum, device.Name, device.IP)
	fmt.Printf("循环完成！总共执行了 %d 次循环\n", randomNum)
}
