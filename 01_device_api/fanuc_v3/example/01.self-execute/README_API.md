# FANUC v3 自执行进程管理器 - API版本

## 概述

这是一个支持从API动态获取设备列表的进程管理器，类似于supervisord的功能，能够：

- 从API定时读取设备配置列表
- 自动启动和管理设备进程
- 支持进程自动重启和故障恢复
- 动态添加和移除设备进程
- 企业级日志记录和轮转

## 环境变量配置

### 必需的环境变量

| 环境变量名 | 描述 | 默认值 | 示例 |
|-----------|------|--------|------|
| `DEVICE_API_URL` | 设备配置API地址 | `http://localhost:9105/api/public/devices/configs` | `http://mdc_server_api:9005/api/public/devices/configs` |
| `DEVICE_REFRESH_INTERVAL` | 设备列表刷新间隔（秒） | `30` | `60` |

### 配置示例

```bash
# 设置API地址
export DEVICE_API_URL="http://localhost:9105/api/public/devices/configs"

# 设置刷新间隔为60秒
export DEVICE_REFRESH_INTERVAL="60"

# 启动进程管理器
./self-execute
```

## API接口要求

### 请求格式
```
GET /api/public/devices/configs
Content-Type: application/json
```

### 响应格式
```json
{
  "success": true,
  "message": "获取设备配置成功",
  "data": [
    {
      "id": "device-001",
      "name": "FANUC设备1",
      "ip": "************",
      "port": 8193,
      "brand": "FANUC",
      "model": "R-30iB",
      "location": "生产线1",
      "data_type": "fanuc",
      "enabled": true,
      "auto_start": true,
      "collect_interval": 1000,
      "timeout": 10,
      "retry_count": 3,
      "retry_delay": 5
    }
  ]
}
```

### 字段说明

- `id`: 设备唯一标识符
- `name`: 设备名称
- `ip`: 设备IP地址
- `port`: 设备端口号
- `brand`: 设备品牌
- `model`: 设备型号
- `location`: 设备位置
- `data_type`: 数据类型
- `enabled`: 是否启用（只有启用的设备才会被管理）
- `auto_start`: 是否自动启动（只有设置为自动启动的设备才会被启动）
- `collect_interval`: 数据采集间隔（毫秒）
- `timeout`: 连接超时时间（秒）
- `retry_count`: 重试次数
- `retry_delay`: 重试延迟（秒，用作进程重启延迟）

## 使用方法

### 1. 编译程序
```bash
cd manage
go build -o self-execute self-execute.go
```

### 2. 设置环境变量
```bash
# 本地开发环境
export DEVICE_API_URL="http://localhost:9105/api/public/devices/configs"
export DEVICE_REFRESH_INTERVAL="30"

# Docker环境
export DEVICE_API_URL="http://mdc_server_api:9005/api/public/devices/configs"
export DEVICE_REFRESH_INTERVAL="60"
```

### 3. 启动进程管理器
```bash
./self-execute
```

## 功能特性

### 动态设备管理
- 定时从API获取最新设备配置
- 自动添加新设备进程
- 自动移除已删除的设备进程
- 支持设备配置热更新

### 进程监控和重启
- 类似supervisord的进程监控
- 自动重启异常退出的进程
- 可配置重启延迟和最大重启次数
- 支持优雅关闭

### 容错机制
- API请求失败时使用默认配置
- 支持重试机制和超时处理
- 网络异常时继续管理现有进程

### 日志记录
- 企业级日志轮转
- 详细的进程状态记录
- 设备添加/移除日志
- API请求和响应日志

## 日志文件

日志文件位于 `logs/` 目录下：
- `self-manage_YYYY-MM-DD.log`: 主程序日志
- 自动轮转，保留30天，最大20MB每个文件

## 故障排除

### 常见问题

1. **API连接失败**
   - 检查 `DEVICE_API_URL` 环境变量是否正确
   - 确认API服务是否正常运行
   - 检查网络连接和防火墙设置

2. **设备进程启动失败**
   - 确认 `self-process` 可执行文件存在
   - 检查设备IP地址是否可达
   - 查看日志文件获取详细错误信息

3. **刷新间隔不生效**
   - 确认 `DEVICE_REFRESH_INTERVAL` 环境变量格式正确（纯数字）
   - 重启进程管理器使配置生效

### 调试模式

程序启动时会输出详细的配置信息：
```
[15:04:05] ========== 进程管理器启动 ==========
[15:04:05] 类似supervisord的自动重启功能已启用
[15:04:05] 支持动态设备列表刷新和新设备自动添加
[15:04:05] 实际配置:
  - API地址: http://localhost:9105/api/public/devices/configs
  - 刷新间隔: 30秒
  - 日志文件: logs/self-manage_2025-07-29.log
```

## 与原版本的区别

| 功能 | 原版本 | API版本 |
|------|--------|---------|
| 设备配置 | 硬编码IP列表 | 从API动态获取 |
| 配置更新 | 需要重启程序 | 自动热更新 |
| 设备管理 | 静态管理 | 动态添加/移除 |
| 配置来源 | 代码内置 | 环境变量+API |
| 容错能力 | 基础 | 增强（API失败回退） |

## 部署建议

### Docker环境
```dockerfile
ENV DEVICE_API_URL="http://mdc_server_api:9005/api/public/devices/configs"
ENV DEVICE_REFRESH_INTERVAL="60"
```

### Kubernetes环境
```yaml
env:
- name: DEVICE_API_URL
  value: "http://mdc-server-api:9005/api/public/devices/configs"
- name: DEVICE_REFRESH_INTERVAL
  value: "60"
```
