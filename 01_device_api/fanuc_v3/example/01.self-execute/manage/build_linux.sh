#!/bin/bash

# Linux x86 交叉编译脚本
# 编译适用于 Linux x86_64 平台的 self-manage 程序

EXECUTE_NAME=self-manage_linux

echo "开始编译 Linux x86_64 版本的进程管理器..."

# 清理旧文件
rm -rf ./$EXECUTE_NAME
# 删除 fanuc_v3 目录的原程序
rm -rf ./../$EXECUTE_NAME

# 设置交叉编译环境变量
export GOOS=linux      # 目标操作系统
export GOARCH=386    # 目标架构 (x86_64)
export CGO_ENABLED=0   # 禁用CGO，确保静态链接

echo "编译配置:"
echo "  目标系统: $GOOS"
echo "  目标架构: $GOARCH"
echo "  CGO状态: $CGO_ENABLED"

# 编译程序
go build -ldflags="-w -s" -o $EXECUTE_NAME ./self-execute.go

# 检查编译是否成功
if [ $? -eq 0 ]; then
    echo "编译成功: $EXECUTE_NAME"

    # 显示文件信息
    ls -la $EXECUTE_NAME
    file $EXECUTE_NAME

    # 移动程序到上级目录
    mv $EXECUTE_NAME ./../$EXECUTE_NAME
    echo "程序已移动到: ../$EXECUTE_NAME"
else
    echo "编译失败!"
    exit 1
fi

echo "Linux x86_64 版本编译完成!"
