# FANUC v2 资源管理说明

## 概述

本文档详细说明FANUC v2程序的资源管理机制，特别是FOCAS句柄的分配、使用和释放过程，确保程序能够长时间稳定运行而不会出现资源泄漏。

## 资源管理策略

### 1. 循环内资源隔离

**问题**: 原始代码在循环中使用 `defer cleanupConnection(libh)` 会导致所有defer调用堆积，直到main函数结束才执行。

**解决方案**: 使用匿名函数包装每次循环，确保资源在每次循环结束时立即释放。

```go
// 循环执行功能测试，确保每次循环后资源正确释放
for i := 0; i <= 99; i++ {
    logger.Infof("准备启动第 %d 轮功能测试", i)
    
    // 使用匿名函数确保每次循环后资源立即释放
    func() {
        // 初始化FOCAS连接
        libh, success := initializeConnection()
        if !success {
            logger.Error("FOCAS连接初始化失败，程序退出")
            os.Exit(1)
        }
        
        // 确保连接在本次循环结束时被清理
        defer func() {
            cleanupConnection(libh)
            logger.Infof("第 %d 轮测试FOCAS连接已清理", i)
        }()

        // 执行功能测试
        executeAllFunctions(libh)
    }()
    
    // 短暂延迟，确保资源完全释放
    time.Sleep(time.Second * 1)
    logger.Infof("第 %d 轮测试完成，资源已释放", i)
}
```

### 2. 程序重启前的资源清理

**目的**: 确保程序重启时所有资源都已完全释放，避免资源冲突。

```go
// 程序重启前的资源清理确认
logger.Info("准备重启程序，确保所有资源已释放...")

// 强制垃圾回收，确保内存释放
runtime.GC()
runtime.GC() // 执行两次确保彻底清理

// 短暂延迟，确保系统资源完全释放
time.Sleep(2 * time.Second)

logger.Info("资源清理完成，开始重启程序...")
```

## 资源释放机制详解

### 1. FOCAS句柄管理

#### 分配过程
```go
func initializeConnection() (C.ushort, bool) {
    // 1. 启动FOCAS进程
    ret := C.cnc_startupprocess(C.long(LOG_LEVEL), log_fname)
    
    // 2. 分配连接句柄
    ret = C.cnc_allclibhndl3(ip, C.ushort(cncPort), 10, &libh)
    
    return libh, true
}
```

#### 释放过程
```go
func cleanupConnection(libh C.ushort) {
    // 1. 释放库句柄
    ret := C.cnc_freelibhndl(libh)
    
    // 2. 退出FOCAS进程
    ret = C.cnc_exitprocess()
}
```

### 2. 内存管理

#### Go内存管理
- **自动垃圾回收**: Go运行时自动管理Go对象的内存
- **强制GC**: 使用 `runtime.GC()` 强制执行垃圾回收
- **CGO内存**: C语言分配的内存需要手动释放

#### C内存管理
```go
// C字符串分配和释放
log_fname := C.CString("focas.log")
defer C.free(unsafe.Pointer(log_fname))

ip := C.CString(cncIP)
defer C.free(unsafe.Pointer(ip))
```

## 资源释放时序

### 单次循环的资源生命周期

1. **资源分配阶段**
   ```
   循环开始 → 匿名函数开始 → initializeConnection() → 分配FOCAS句柄
   ```

2. **资源使用阶段**
   ```
   执行功能测试 → 读取CNC数据 → 记录日志
   ```

3. **资源释放阶段**
   ```
   匿名函数结束 → defer执行 → cleanupConnection() → 释放FOCAS句柄
   ```

4. **确认释放阶段**
   ```
   延迟等待 → 日志记录 → 下一次循环
   ```

### 程序重启的资源生命周期

1. **循环完成阶段**
   ```
   所有循环完成 → 所有FOCAS句柄已释放
   ```

2. **内存清理阶段**
   ```
   runtime.GC() → 强制垃圾回收 → 内存释放
   ```

3. **系统资源等待**
   ```
   time.Sleep(2s) → 确保系统资源完全释放
   ```

4. **程序重启阶段**
   ```
   exec.Command() → 启动新进程 → 旧进程退出
   ```

## 资源泄漏预防

### 1. 句柄泄漏预防
- ✅ 每次循环使用匿名函数隔离
- ✅ defer确保异常情况下也能释放资源
- ✅ 详细日志记录句柄分配和释放
- ✅ 循环间延迟确保资源完全释放

### 2. 内存泄漏预防
- ✅ C字符串使用defer释放
- ✅ 程序重启前强制GC
- ✅ 避免全局变量累积
- ✅ 及时释放不再使用的对象

### 3. 系统资源预防
- ✅ 文件句柄正确关闭
- ✅ 网络连接正确断开
- ✅ 进程资源正确清理
- ✅ 重启前充分等待

## 监控和调试

### 1. 日志监控
```bash
# 监控资源分配和释放
grep "连接\|清理\|句柄" logs/fanuc_v2_demo.log

# 监控循环执行
grep "轮测试" logs/fanuc_v2_demo.log

# 监控程序重启
grep "重启\|资源清理" logs/fanuc_v2_demo.log
```

### 2. 系统监控
```bash
# 监控进程资源使用
ps aux | grep fanuc_v2

# 监控内存使用
top -p $(pgrep fanuc_v2)

# 监控文件句柄
lsof -p $(pgrep fanuc_v2)
```

## 最佳实践

### 1. 资源使用原则
- **及时释放**: 不再使用的资源立即释放
- **异常安全**: 使用defer确保异常情况下也能释放
- **监控记录**: 详细记录资源的分配和释放
- **定期清理**: 定期执行垃圾回收和资源清理

### 2. 程序设计原则
- **资源隔离**: 使用函数作用域隔离资源
- **错误处理**: 完善的错误处理和恢复机制
- **状态监控**: 实时监控程序运行状态
- **优雅重启**: 确保重启过程中资源正确处理

## 总结

通过以上资源管理机制，FANUC v2程序能够：

1. **避免句柄泄漏**: 每次循环后FOCAS句柄都会被正确释放
2. **防止内存泄漏**: 强制垃圾回收和C内存手动释放
3. **确保稳定重启**: 重启前充分清理所有资源
4. **支持长期运行**: 可以无限循环运行而不会资源耗尽

这种设计确保了程序的长期稳定性和可靠性。
