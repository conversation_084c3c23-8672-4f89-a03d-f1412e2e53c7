/**
 * FANUC FOCAS2/Ethernet 完整功能演示程序
 *
 * 功能概述：
 * 本程序是基于FANUC FOCAS2库的完整功能演示程序，实现了与FANUC CNC控制系统的
 * 以太网通信，展示了FOCAS2库的所有主要功能，包括系统信息读取、轴位置监控、
 * 程序管理、状态监控、报警处理等核心功能
 *
 * 主要功能：
 * - 连接管理：建立和管理与FANUC CNC系统的以太网连接
 * - 系统信息：读取CNC ID、系统配置、轴名称等基础信息
 * - 位置监控：实时读取各轴的绝对位置、机械位置、相对位置
 * - 动态数据：获取进给速度、主轴转速等实时运行数据
 * - 程序管理：读取当前程序号、序列号、执行程序名等
 * - 状态监控：监控设备运行状态、报警状态、模式状态
 * - 参数读取：读取CNC系统的配置参数和诊断数据
 * - 刀具管理：读取刀具偏置信息和刀具数据
 * - 负载监控：监控伺服负载和主轴负载信息
 *
 * 技术特性：
 * - CGO集成：使用CGO技术调用FANUC FOCAS2 C语言库
 * - 内存安全：正确处理C语言内存分配和释放
 * - 错误处理：完善的错误检查和异常处理机制
 * - 数据转换：C语言数据类型与Go语言类型的安全转换
 * - 联合体处理：解决Go语言中C联合体的访问问题
 * - 指针操作：安全的unsafe指针操作和类型转换
 *
 * 架构设计：
 * - 模块化：按功能模块组织代码，便于维护和扩展
 * - 分层设计：连接层、数据层、业务层的清晰分离
 * - 错误隔离：单个功能失败不影响其他功能的执行
 * - 资源管理：正确的连接建立、使用和释放流程
 *
 * 应用场景：
 * - 设备监控：实时监控FANUC CNC设备的运行状态
 * - 数据采集：采集设备的位置、速度、负载等数据
 * - 系统集成：与MES、ERP等上层系统的数据集成
 * - 故障诊断：通过报警和诊断数据进行故障分析
 * - 性能分析：通过负载和效率数据进行性能优化
 *
 * 依赖库：
 * - FOCAS2库：FANUC官方提供的CNC通信库
 * - fwlib32.h：FOCAS2库的头文件定义
 * - libfwlib32.so：Linux平台的FOCAS2动态链接库
 *
 * 编译要求：
 * - CGO_ENABLED=1：启用CGO支持
 * - 正确的库文件路径配置
 * - 对应平台的FOCAS2库文件
 *
 * @package main
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */
package main

/*
CGO编译配置和C语言接口定义

编译标志说明：
- CFLAGS: 指定C语言头文件的搜索路径，./fwlib目录包含FOCAS2库的头文件
- LDFLAGS: 指定链接器参数
  - -L./fwlib: 指定库文件搜索路径
  - -lfwlib32: 链接FOCAS2库文件
  - -Wl,-rpath=./fwlib: 设置运行时库文件搜索路径

头文件包含：
- stdlib.h: 标准C库，提供内存管理函数
- string.h: 字符串处理函数
- fwlib32.h: FANUC FOCAS2库的主要头文件
*/

/*
#cgo CFLAGS: -I./fwlib
#cgo LDFLAGS: -L./fwlib -lfwlib32 -Wl,-rpath=./fwlib
#include <stdlib.h>
#include <string.h>
#include "fwlib32.h"

typedef struct odbdy2_t {
    short dummy;
    short axis;
    long alarm;
    long prgnum;
    long prgmnum;
    long seqnum;
    long actf;
    long acts;
    union {
        struct {
            long absolute[MAX_AXIS];
            long machine[MAX_AXIS];
            long relative[MAX_AXIS];
            long distance[MAX_AXIS];
        } faxis;
        struct {
            long absolute;
            long machine;
            long relative;
            long distance;
        } oaxis;
    } pos;
} ODBDY2_T;

typedef struct odbdy2_t_pos {
    struct {
        long absolute[MAX_AXIS];
        long machine[MAX_AXIS];
        long relative[MAX_AXIS];
        long distance[MAX_AXIS];
    } faxis;
} ODBDY2_T_POS;
*/
import "C"

/**
 * Go语言标准库和项目模块导入
 *
 * 导入的标准库说明：
 * - fmt: 格式化输入输出，用于打印调试信息和结果展示
 * - os: 操作系统接口，用于程序退出和系统调用
 * - strings: 字符串处理，用于字符串操作和格式化
 * - time: 时间处理，用于时间戳和时间格式化
 * - unsafe: 不安全指针操作，用于C语言数据结构的访问和转换
 *
 * 项目模块说明：
 * - ./logger: 企业级日志管理模块，提供结构化日志记录和轮转功能
 *
 * unsafe包的使用说明：
 * 在与C语言交互时，经常需要进行指针类型转换和内存操作
 * unsafe包提供了绕过Go类型安全检查的能力，但需要谨慎使用
 */
import (
	/** 命令行参数解析，用于接收IP和端口参数 */
	"flag"
	"fmt" /** 格式化输入输出，用于结果展示和调试信息打印 */
	"log"
	"os" /** 操作系统接口，用于程序退出和错误处理 */
	"os/exec"
	"strings" /** 字符串处理，用于字符串操作和格式化 */
	"time"    /** 时间处理，用于时间戳生成和格式化 */
	"unsafe"  /** 不安全指针操作，用于C语言数据结构访问 */

	"fanuc_v2/logger" /** 企业级日志管理模块，提供结构化日志记录和轮转功能 */
)

/**
 * 全局配置变量
 *
 * 配置说明：
 * - cncIP: FANUC CNC控制系统的IP地址，可通过命令行参数 -ip 指定
 * - cncPort: FOCAS2通信端口，可通过命令行参数 -port 指定，FANUC标准端口为8193
 * - LOG_LEVEL: FOCAS2库的日志级别，0表示最详细的日志输出
 *
 * 命令行参数：
 * - -ip string: 指定FANUC CNC设备的IP地址 (默认: "**************")
 * - -port int: 指定FOCAS2通信端口 (默认: 8193)
 * - -h: 显示帮助信息
 *
 * 使用示例：
 * - ./fanuc_v2_test -ip ************* -port 8193
 * - ./fanuc_v2_test -ip *********
 * - ./fanuc_v2_test -h
 *
 * 注意事项：
 * - IP地址需要与实际FANUC设备的网络配置匹配
 * - 端口号通常为8193，但可能因设备配置而异
 * - 日志级别影响FOCAS2库的调试信息输出量
 */
var (
	/** FANUC CNC控制系统的IP地址，可通过命令行参数指定 */
	cncIP string

	/** FOCAS2通信端口，可通过命令行参数指定 */
	cncPort int

	/** FOCAS2库日志级别，0=详细，1=普通，2=错误 */
	LOG_LEVEL = 0
)

/**
 * 命令行参数解析函数
 *
 * 功能：解析命令行参数，设置CNC设备的IP地址和端口号
 *
 * 支持的参数：
 * - -ip string: FANUC CNC设备的IP地址
 * - -port int: FOCAS2通信端口号
 * - -h: 显示帮助信息
 *
 * 默认值：
 * - IP地址: **************
 * - 端口号: 8193
 *
 * 使用示例：
 * - ./program -ip ************* -port 8193
 * - ./program -ip *********
 * - ./program -h
 */
func parseCommandLineArgs() {
	// 定义命令行参数
	flag.StringVar(&cncIP, "ip", "**************",
		"FANUC CNC设备的IP地址 (例如: *************)")

	flag.IntVar(&cncPort, "port", 8193,
		"FOCAS2通信端口号 (FANUC标准端口: 8193)")

	// 自定义帮助信息
	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "FANUC FOCAS2/Ethernet 完整功能演示程序\n\n")
		fmt.Fprintf(os.Stderr, "用法: %s [选项]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "选项:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\n示例:\n")
		fmt.Fprintf(os.Stderr, "  %s -ip ************* -port 8193\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -ip *********\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -h\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "\n注意:\n")
		fmt.Fprintf(os.Stderr, "  - IP地址必须是可访问的FANUC CNC设备地址\n")
		fmt.Fprintf(os.Stderr, "  - 端口号通常为8193，但可能因设备配置而异\n")
		fmt.Fprintf(os.Stderr, "  - 程序会尝试连接到指定的设备并读取各种数据\n")
	}

	// 解析命令行参数
	flag.Parse()

	// 记录解析结果到日志（如果日志系统已初始化）
	logger.Infof("命令行参数解析完成: IP=%s, Port=%d", cncIP, cncPort)
}

/**
 * 错误处理辅助函数
 *
 * 功能：统一处理FOCAS2函数的返回值，提供一致的错误检查和日志输出
 *
 * @param {string} funcName - 调用的FOCAS2函数名称，用于错误日志
 * @param {C.short} ret - FOCAS2函数的返回值
 * @returns {bool} true表示成功，false表示失败
 *
 * 错误码说明：
 * - EW_OK (0): 操作成功
 * - 其他值: 各种错误码，具体含义参考FOCAS2文档
 *
 * 日志记录：
 * - 成功时记录Info级别日志，便于跟踪函数调用
 * - 失败时记录Error级别日志，便于问题排查
 * - 同时输出到控制台，便于实时监控
 *
 * @example
 * ret := C.cnc_allclibhndl3(ip, port, timeout, &libh)
 * if !checkError("cnc_allclibhndl3", ret) {
 *     return false
 * }
 */
func checkError(funcName string, ret C.short) bool {
	/** 检查返回值是否为成功状态 */
	if ret != C.EW_OK {
		/** 记录错误日志和控制台输出 */
		logger.Errorf("FOCAS函数调用失败: %s (错误码: %d)", funcName, ret)
		fmt.Printf("❌ %s failed (error code: %d)\n", funcName, ret)
		return false
	}
	/** 记录成功日志和控制台输出 */
	logger.Debugf("FOCAS函数调用成功: %s", funcName)
	fmt.Printf("✅ %s success\n", funcName)
	return true
}

/**
 * 打印主标题分隔线
 *
 * 功能：打印格式化的主标题，用于区分不同的功能模块
 *
 * @param {string} title - 要显示的标题文本
 *
 * 输出格式：
 * ============================================================
 *   标题文本
 * ============================================================
 */
func printSeparator(title string) {
	/** 打印上边框线 */
	fmt.Printf("\n" + strings.Repeat("=", 60) + "\n")
	/** 打印居中的标题文本 */
	fmt.Printf("  %s\n", title)
	/** 打印下边框线 */
	fmt.Printf(strings.Repeat("=", 60) + "\n")
}

/**
 * 打印子标题分隔线
 *
 * 功能：打印格式化的子标题，用于区分功能模块内的不同子功能
 *
 * @param {string} title - 要显示的子标题文本
 *
 * 输出格式：
 * ----------------------------------------
 *   子标题文本
 * ----------------------------------------
 */
func printSubTitle(title string) {
	/** 打印上边框线 */
	fmt.Printf("\n" + strings.Repeat("-", 40) + "\n")
	/** 打印居中的子标题文本 */
	fmt.Printf("  %s\n", title)
	/** 打印下边框线 */
	fmt.Printf(strings.Repeat("-", 40) + "\n")
}

/**
 * main 函数 - FOCAS2/Ethernet 完整功能演示
 *
 * 功能：程序主入口，负责初始化日志系统、建立FOCAS连接并执行功能测试
 *
 * 执行流程：
 * 1. 初始化企业级日志系统，配置日志轮转和输出格式
 * 2. 记录程序启动信息和目标设备配置
 * 3. 建立与FANUC CNC系统的FOCAS连接
 * 4. 执行所有功能模块的测试和演示
 * 5. 清理连接资源并记录程序结束信息
 *
 * 日志配置：
 * - 日志级别：info（记录关键信息和错误）
 * - 日志格式：text（便于开发调试的可读格式）
 * - 输出位置：logs/fanuc_v2_demo.log（专用演示日志文件）
 * - 轮转策略：100MB/文件，保留7天，最多10个备份文件，自动压缩
 */
func main() {
	// 1. 解析命令行参数
	parseCommandLineArgs()

	logFile := fmt.Sprintf("logs/%s_%d_fanuc_v2_demo.log", cncIP, cncPort)

	// 2. 初始化企业级日志系统
	logger.InitLoggerWithRotation(
		"info",  // 日志级别：记录关键信息和错误
		"text",  // 日志格式：文本格式便于阅读
		logFile, // 日志文件：保存到logs目录
		logger.LogRotationConfig{ // 日志轮转配置
			Enabled:    true, // 启用日志轮转
			MaxSize:    100,  // 单文件最大100MB
			MaxAge:     7,    // 保留7天
			MaxBackups: 10,   // 最多10个备份文件
			Compress:   true, // 自动压缩历史文件
		},
	)

	// 3. 记录程序启动信息
	printSeparator("FANUC FOCAS2/Ethernet 完整功能演示程序")
	logger.Infof("程序启动 - FANUC FOCAS2/Ethernet 完整功能演示")
	logger.Infof("目标设备: %s:%d", cncIP, cncPort)
	logger.Infof("启动时间: %s", time.Now().Format("2006-01-02 15:04:05"))
	logger.Infof("日志文件: logs/fanuc_v2_demo.log")

	// 同时输出到控制台便于实时查看
	fmt.Printf("目标设备: %s:%d\n", cncIP, cncPort)
	fmt.Printf("启动时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))
	fmt.Printf("日志文件: logs/fanuc_v2_demo.log\n")

	// 循环执行功能测试，确保每次循环后资源正确释放
	for i := 0; i < 10; i++ {
		logger.Infof(" %s 准备启动第 %d 轮功能测试", cncIP, i)

		// 使用匿名函数确保每次循环后资源立即释放
		func() {

			// defer 有异常
			if deferErr := recover(); deferErr != nil {
				logger.Errorf("第 %d 轮测试发生异常: %v", i, deferErr)
			}

			// 4. 初始化FOCAS连接
			logger.Info("开始初始化FOCAS连接...")
			libh, success := initializeConnection()
			if !success {
				logger.Error("FOCAS连接初始化失败，程序退出")
				// os.Exit(1)
				return
			}

			// 确保连接在本次循环结束时被清理
			defer func() {
				cleanupConnection(libh)
				logger.Infof("第 %d 轮测试FOCAS连接已清理", i)
			}()

			// 5. 执行所有功能测试
			logger.Info("开始执行功能测试...")
			logger.Infof("第 %d 轮功能测试开始", i)
			executeAllFunctions(libh)
			logger.Infof("第 %d 轮功能测试完成", i)
		}()

		// 短暂延迟，确保资源完全释放
		time.Sleep(time.Second * 1)
		logger.Infof("第 %d 轮测试完成，资源已释放", i)
	}
	// executeAllFunctions(libh)

	// 6. 记录程序结束信息
	logger.Info("所有功能测试完成，程序正常结束")

	// 程序自动重新启动运行, 包含命令行参数
	cmd := exec.Command(os.Args[0], os.Args[1:]...)
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	err := cmd.Run()
	if err != nil {
		logger.Errorf("程序重启失败: %v", err)
	}

	log.Println("New process started, exiting current process...")

	// 退出当前进程
	os.Exit(0)
}

/**
 * 初始化FOCAS连接
 *
 * 功能：建立与FANUC CNC系统的FOCAS2/Ethernet连接
 *
 * 执行步骤：
 * 1. 启动FOCAS进程，初始化FOCAS2库环境
 * 2. 建立与目标CNC设备的网络连接
 * 3. 获取连接句柄，用于后续的数据通信
 *
 * @returns {C.ushort, bool} 连接句柄和成功标志
 */
func initializeConnection() (C.ushort, bool) {
	printSubTitle("1. 连接管理功能")
	logger.Info("开始初始化FOCAS连接管理功能")

	var libh C.ushort

	// 1. 启动FOCAS进程
	logger.Infof("启动FOCAS进程，日志级别: %d", LOG_LEVEL)

	// 定义FOCAS日志文件名（Go字符串）
	focasLogFile := "focas.log"

	// 检查FOCAS日志文件是否存在，如果存在则删除它
	if _, err := os.Stat(focasLogFile); err == nil {
		logger.Infof("FOCAS日志文件已存在，正在删除: %s", focasLogFile)
		if err := os.Remove(focasLogFile); err != nil {
			logger.Errorf("删除FOCAS日志文件失败: %v", err)
		} else {
			logger.Info("FOCAS日志文件删除成功")
		}
	}

	// 创建C字符串用于FOCAS函数调用
	log_fname := C.CString(focasLogFile)
	defer C.free(unsafe.Pointer(log_fname))

	ret := C.cnc_startupprocess(C.long(LOG_LEVEL), log_fname)
	if !checkError("cnc_startupprocess", ret) {
		logger.Error("FOCAS进程启动失败")
		return 0, false
	}
	logger.Info("FOCAS进程启动成功")

	// 2. 连接到CNC系统
	ip := C.CString(cncIP)
	defer C.free(unsafe.Pointer(ip))

	logger.Infof("正在连接到FANUC CNC设备: %s:%d", cncIP, cncPort)
	fmt.Printf("正在连接到 %s:%d...\n", cncIP, cncPort)

	ret = C.cnc_allclibhndl3(ip, C.ushort(cncPort), 10, &libh)
	if !checkError("cnc_allclibhndl3", ret) {
		logger.Error("CNC设备连接失败，正在清理FOCAS进程")
		C.cnc_exitprocess()
		return 0, false
	}

	// 3. 连接成功
	logger.Infof("成功连接到FANUC CNC系统，连接句柄: %d", libh)
	fmt.Printf("🔗 成功连接到FANUC CNC系统 (句柄: %d)\n", libh)
	return libh, true
}

/**
 * 清理FOCAS连接
 *
 * 功能：安全地释放FOCAS连接资源，确保系统资源正确回收
 *
 * 清理步骤：
 * 1. 释放FOCAS库句柄，断开与CNC设备的连接
 * 2. 退出FOCAS进程，清理FOCAS2库环境
 * 3. 记录连接清理完成信息
 *
 * @param {C.ushort} libh - 要释放的FOCAS连接句柄
 */
func cleanupConnection(libh C.ushort) {
	printSubTitle("清理连接")
	logger.Info("开始清理FOCAS连接资源")

	// 1. 释放库句柄
	logger.Infof("释放FOCAS库句柄: %d", libh)
	ret := C.cnc_freelibhndl(libh)
	if checkError("cnc_freelibhndl", ret) {
		logger.Info("FOCAS库句柄释放成功")
	} else {
		logger.Error("FOCAS库句柄释放失败")
	}

	// 2. 退出FOCAS进程
	logger.Info("退出FOCAS进程")
	ret = C.cnc_exitprocess()
	if checkError("cnc_exitprocess", ret) {
		logger.Info("FOCAS进程退出成功")
	} else {
		logger.Error("FOCAS进程退出失败")
	}

	// 3. 记录清理完成
	logger.Info("FOCAS连接资源清理完成")
	fmt.Printf("🔌 连接已断开\n")
}

/**
 * 执行所有功能测试
 *
 * 功能：依次执行FOCAS2库的各项功能测试，展示完整的CNC数据采集能力
 *
 * 测试模块：
 * 1. 系统信息读取 - CNC ID、系统配置、轴名称等基础信息
 * 2. 轴位置和动态数据 - 实时位置、速度、主轴转速等运行数据
 * 3. 程序信息 - 当前程序号、序列号、执行程序名等程序状态
 * 4. 状态信息 - 设备运行状态、模式状态等状态监控
 * 5. 参数信息 - CNC系统参数和配置信息
 *
 * @param {C.ushort} libh - FOCAS连接句柄
 */
func executeAllFunctions(libh C.ushort) {
	logger.Info("开始执行FOCAS功能测试模块")

	// 功能1: 系统信息读取
	logger.Info("执行功能模块1: 系统信息读取")
	readSystemInfo(libh)

	// 功能2: 轴位置和动态数据
	// logger.Info("执行功能模块2: 轴位置和动态数据")
	// readAxisPositions(libh)

	// 功能3: 程序信息
	// logger.Info("执行功能模块3: 程序信息")
	// readProgramInfo(libh)

	// 功能4: 状态信息
	// logger.Info("执行功能模块4: 状态信息")
	// readStatusInfo(libh)

	// 功能5: 报警信息 (暂时注释)
	//logger.Info("执行功能模块5: 报警信息")
	//readAlarmInfo(libh)

	// 功能6: 参数信息
	// logger.Info("执行功能模块6: 参数信息")
	// readParameterInfo(libh)

	// 功能7: 诊断数据 (暂时注释)
	//logger.Info("执行功能模块7: 诊断数据")
	//readDiagnosticData(libh)

	// 功能8: 刀具信息 (暂时注释)
	//logger.Info("执行功能模块8: 刀具信息")
	//readToolInfo(libh)

	// 功能9: 负载信息 (暂时注释)
	//logger.Info("执行功能模块9: 负载信息")
	//readLoadInfo(libh)

	logger.Info("所有FOCAS功能测试模块执行完成")
	printSeparator("所有功能测试完成")
}

/**
 * 功能1: 系统信息读取
 *
 * 功能：读取FANUC CNC系统的基础信息，包括设备标识、系统配置、运行状态等
 *
 * 读取内容：
 * - CNC ID: 设备唯一标识符
 * - 系统信息: CNC类型、版本、轴配置等
 * - 状态信息: 运行状态、报警状态、模式状态等
 * - 轴名称: 各轴的名称配置
 *
 * @param {C.ushort} libh - FOCAS连接句柄
 */
func readSystemInfo(libh C.ushort) {
	printSubTitle("2. 系统信息读取功能")
	logger.Info("开始读取CNC系统信息")

	// 2.1 读取CNC ID
	logger.Debug("读取CNC设备ID")
	readCNCID(libh)

	// 2.2 读取系统信息
	// logger.Debug("读取CNC系统配置信息")
	// readSystemInformation(libh)

	// 2.3 读取状态信息
	// logger.Debug("读取CNC运行状态信息")
	// readStatInfo(libh)

	// 2.4 读取轴名称
	// logger.Debug("读取CNC轴名称配置")
	// readAxisNames(libh)

	// logger.Info("CNC系统信息读取完成")
}

/**
 * 2.1 读取CNC ID
 *
 * 功能：读取FANUC CNC设备的唯一标识符，用于设备识别和管理
 *
 * @param {C.ushort} libh - FOCAS连接句柄
 */
func readCNCID(libh C.ushort) {
	fmt.Printf("\n📋 读取CNC ID:\n")
	logger.Debug("开始读取CNC设备ID")

	var cnc_ids [4]C.ulong
	ret := C.cnc_rdcncid(libh, &cnc_ids[0])

	if checkError("cnc_rdcncid", ret) {
		machine_id := fmt.Sprintf("%08x-%08x-%08x-%08x",
			cnc_ids[0], cnc_ids[1], cnc_ids[2], cnc_ids[3])

		// 记录到日志
		logger.Infof("CNC设备ID读取成功: %s", machine_id)
		logger.Debugf("CNC ID详情: ID1=0x%08x(%d), ID2=0x%08x(%d), ID3=0x%08x(%d), ID4=0x%08x(%d)",
			cnc_ids[0], cnc_ids[0], cnc_ids[1], cnc_ids[1],
			cnc_ids[2], cnc_ids[2], cnc_ids[3], cnc_ids[3])

		// 输出到控制台
		fmt.Printf("  🆔 机器ID: %s\n", machine_id)
		fmt.Printf("  📊 ID详情:\n")
		fmt.Printf("    - ID1: 0x%08x (%d)\n", cnc_ids[0], cnc_ids[0])
		fmt.Printf("    - ID2: 0x%08x (%d)\n", cnc_ids[1], cnc_ids[1])
		fmt.Printf("    - ID3: 0x%08x (%d)\n", cnc_ids[2], cnc_ids[2])
		fmt.Printf("    - ID4: 0x%08x (%d)\n", cnc_ids[3], cnc_ids[3])
	} else {
		logger.Error("CNC设备ID读取失败")
	}
}

/**
 * 2.2 读取系统信息
 *
 * 功能：读取FANUC CNC系统的详细配置信息，包括类型、版本、轴配置等
 *
 * @param {C.ushort} libh - FOCAS连接句柄
 */
func readSystemInformation(libh C.ushort) {
	fmt.Printf("\n🖥️  读取系统信息:\n")
	logger.Debug("开始读取CNC系统配置信息")

	var sysinfo C.ODBSYS
	ret := C.cnc_sysinfo(libh, &sysinfo)

	if checkError("cnc_sysinfo", ret) {
		// 提取系统信息
		cncType := C.GoString(&sysinfo.cnc_type[0])
		mtType := C.GoString(&sysinfo.mt_type[0])
		series := C.GoString(&sysinfo.series[0])
		version := C.GoString(&sysinfo.version[0])
		axes := C.GoString(&sysinfo.axes[0])

		// 记录到日志
		logger.Infof("CNC系统信息读取成功: 类型=%s, 机床类型=%s, 系列=%s, 版本=%s, 最大轴数=%d",
			cncType, mtType, series, version, sysinfo.max_axis)
		logger.Debugf("CNC轴配置: %s", axes)

		// 输出到控制台
		fmt.Printf("  📋 系统详情:\n")
		fmt.Printf("    - 附加信息: %d\n", sysinfo.addinfo)
		fmt.Printf("    - 最大轴数: %d\n", sysinfo.max_axis)
		fmt.Printf("    - CNC类型: %s\n", cncType)
		fmt.Printf("    - 机床类型: %s\n", mtType)
		fmt.Printf("    - 系列: %s\n", series)
		fmt.Printf("    - 版本: %s\n", version)
		fmt.Printf("    - 轴名称: %s\n", axes)
	} else {
		logger.Error("CNC系统信息读取失败")
	}
}

// 2.3 读取状态信息
func readStatInfo(libh C.ushort) {
	fmt.Printf("\n📊 读取状态信息:\n")

	var statinfo C.ODBST
	ret := C.cnc_statinfo(libh, &statinfo)

	if checkError("cnc_statinfo", ret) {
		fmt.Printf("  🔄 运行状态:\n")
		fmt.Printf("    - 报警状态: %d\n", statinfo.alarm)
		fmt.Printf("    - 自动模式: %d\n", statinfo.aut)
		fmt.Printf("    - 编辑状态: %d\n", statinfo.edit)
		fmt.Printf("    - 急停状态: %d\n", statinfo.emergency)
		fmt.Printf("    - 手轮状态: %d\n", statinfo.hdck)
		fmt.Printf("    - 运动状态: %d\n", statinfo.motion)
		fmt.Printf("    - 主轴状态: %d\n", statinfo.mstb)
		fmt.Printf("    - 运行状态: %d\n", statinfo.run)
		fmt.Printf("    - 时间模式: %d\n", statinfo.tmmode)

		// 解释运行状态
		runStatus := getRunStatusDescription(int(statinfo.run))
		fmt.Printf("    - 运行状态说明: %s\n", runStatus)
	}
}

// 获取运行状态描述
func getRunStatusDescription(status int) string {
	switch status {
	case 0:
		return "停止"
	case 1:
		return "保持"
	case 2:
		return "启动"
	case 3:
		return "运行中"
	case 4:
		return "中断"
	default:
		return fmt.Sprintf("未知状态(%d)", status)
	}
}

// 2.4 读取轴名称
func readAxisNames(libh C.ushort) {
	fmt.Printf("\n🎯 读取轴名称:\n")

	var axes [C.MAX_AXIS]C.ODBAXISNAME
	var cnt C.short = C.MAX_AXIS
	ret := C.cnc_rdaxisname(libh, &cnt, (*C.ODBAXISNAME)(unsafe.Pointer(&axes)))

	if checkError("cnc_rdaxisname", ret) {
		fmt.Printf("  📋 轴配置 (共%d轴):\n", cnt)
		for i := 0; i < int(cnt) && i < 8; i++ {
			axisName := C.GoString(&axes[i].name)
			fmt.Printf("    - 轴%d: %s\n", i+1, axisName)
		}
	}
}

/**
 * 功能2: 轴位置和动态数据
 *
 * 功能：读取FANUC CNC设备的实时轴位置和动态运行数据
 *
 * 读取内容：
 * - 绝对位置: 各轴的绝对坐标位置
 * - 机械位置: 各轴的机械坐标位置
 * - 相对位置: 各轴的相对坐标位置
 * - 剩余距离: 各轴到目标位置的剩余距离
 * - 实际进给速度: 当前的进给速度
 * - 实际主轴转速: 当前的主轴转速
 * - 综合动态数据: 所有动态数据的汇总
 *
 * @param {C.ushort} libh - FOCAS连接句柄
 */
func readAxisPositions(libh C.ushort) {
	printSubTitle("3. 轴位置和动态数据功能")
	logger.Info("开始读取轴位置和动态数据")

	// 3.1 读取绝对位置
	logger.Debug("读取各轴绝对位置")
	readAbsolutePosition(libh)

	// 3.2 读取机械位置
	logger.Debug("读取各轴机械位置")
	readMachinePosition(libh)

	// 3.3 读取相对位置
	logger.Debug("读取各轴相对位置")
	readRelativePosition(libh)

	// 3.4 读取剩余距离
	logger.Debug("读取各轴剩余距离")
	readDistanceToGo(libh)

	// 3.5 读取实际进给速度
	logger.Debug("读取实际进给速度")
	readActualFeedrate(libh)

	// 3.6 读取实际主轴转速
	logger.Debug("读取实际主轴转速")
	readActualSpindleSpeed(libh)

	// 3.7 读取所有动态数据
	logger.Debug("读取综合动态数据")
	readAllDynamicData(libh)

	logger.Info("轴位置和动态数据读取完成")
}

// 3.1 读取绝对位置
func readAbsolutePosition(libh C.ushort) {
	fmt.Printf("\n📍 读取绝对位置:\n")

	var axis_data C.ODBAXIS
	// 使用ALL_AXES和正确的长度
	ret := C.cnc_absolute(libh, C.ALL_AXES, 8, &axis_data)

	if checkError("cnc_absolute", ret) {
		// 获取实际轴数，如果为-1则使用7轴
		axisCount := int(axis_data._type)
		if axisCount == -1 {
			axisCount = 7 // 根据轴名称读取结果，我们知道有7轴
		}

		fmt.Printf("  🎯 绝对位置 (轴数: %d):\n", axisCount)
		for i := 0; i < axisCount && i < 8; i++ {
			axisName := getAxisName(i)
			position := axis_data.data[i]
			fmt.Printf("    - %s轴: %d\n", axisName, position)
		}
	}
}

// 3.2 读取机械位置
func readMachinePosition(libh C.ushort) {
	fmt.Printf("\n🔧 读取机械位置:\n")

	var axis_data C.ODBAXIS
	ret := C.cnc_machine(libh, C.ALL_AXES, 8, &axis_data)

	if checkError("cnc_machine", ret) {
		axisCount := int(axis_data._type)
		if axisCount == -1 {
			axisCount = 7
		}

		fmt.Printf("  ⚙️  机械位置 (轴数: %d):\n", axisCount)
		for i := 0; i < axisCount && i < 8; i++ {
			axisName := getAxisName(i)
			position := axis_data.data[i]
			fmt.Printf("    - %s轴: %d\n", axisName, position)
		}
	}
}

// 3.3 读取相对位置
func readRelativePosition(libh C.ushort) {
	fmt.Printf("\n📐 读取相对位置:\n")

	var axis_data C.ODBAXIS
	ret := C.cnc_relative(libh, C.ALL_AXES, 8, &axis_data)

	if checkError("cnc_relative", ret) {
		axisCount := int(axis_data._type)
		if axisCount == -1 {
			axisCount = 7
		}

		fmt.Printf("  📏 相对位置 (轴数: %d):\n", axisCount)
		for i := 0; i < axisCount && i < 8; i++ {
			axisName := getAxisName(i)
			position := axis_data.data[i]
			fmt.Printf("    - %s轴: %d\n", axisName, position)
		}
	}
}

// 3.4 读取剩余距离
func readDistanceToGo(libh C.ushort) {
	fmt.Printf("\n🎯 读取剩余距离:\n")

	var axis_data C.ODBAXIS
	ret := C.cnc_distance(libh, C.ALL_AXES, 8, &axis_data)

	if checkError("cnc_distance", ret) {
		axisCount := int(axis_data._type)
		if axisCount == -1 {
			axisCount = 7
		}

		fmt.Printf("  📊 剩余距离 (轴数: %d):\n", axisCount)
		for i := 0; i < axisCount && i < 8; i++ {
			axisName := getAxisName(i)
			distance := axis_data.data[i]
			fmt.Printf("    - %s轴: %d\n", axisName, distance)
		}
	}
}

/**
 * 3.5 读取实际进给速度
 *
 * 功能：读取CNC设备当前的实际进给速度
 *
 * @param {C.ushort} libh - FOCAS连接句柄
 */
func readActualFeedrate(libh C.ushort) {
	fmt.Printf("\n⚡ 读取实际进给速度:\n")
	logger.Debug("开始读取实际进给速度")

	var feedrate C.ODBACT
	ret := C.cnc_actf(libh, &feedrate)

	if checkError("cnc_actf", ret) {
		logger.Infof("实际进给速度读取成功: %d mm/min", feedrate.data)
		fmt.Printf("  🏃 实际进给速度: %d mm/min\n", feedrate.data)
	} else {
		logger.Error("实际进给速度读取失败")
	}
}

/**
 * 3.6 读取实际主轴转速
 *
 * 功能：读取CNC设备当前的实际主轴转速
 *
 * @param {C.ushort} libh - FOCAS连接句柄
 */
func readActualSpindleSpeed(libh C.ushort) {
	fmt.Printf("\n🌀 读取实际主轴转速:\n")
	logger.Debug("开始读取实际主轴转速")

	var spindle C.ODBACT
	ret := C.cnc_acts(libh, &spindle)

	if checkError("cnc_acts", ret) {
		logger.Infof("实际主轴转速读取成功: %d rpm", spindle.data)
		fmt.Printf("  🔄 实际主轴转速: %d rpm\n", spindle.data)
	} else {
		logger.Error("实际主轴转速读取失败")
	}
}

// 获取轴名称
func getAxisName(index int) string {
	axisNames := []string{"X", "Y", "Z", "A", "B", "C", "U", "V", "W"}
	if index < len(axisNames) {
		return axisNames[index]
	}
	return fmt.Sprintf("轴%d", index+1)
}

/**
 * 3.7 读取所有动态数据
 *
 * 功能：一次性读取CNC设备的所有动态数据，包括位置、速度、程序状态等
 *
 * @param {C.ushort} libh - FOCAS连接句柄
 */
func readAllDynamicData(libh C.ushort) {
	fmt.Printf("\n📊 读取所有动态数据:\n")
	logger.Debug("开始读取综合动态数据")

	var dynamic C.ODBDY2_T
	// 使用参考代码的方式
	ret := C.cnc_rddynamic2(libh, C.ALL_AXES, C.sizeof_ODBDY2_T, (*C.ODBDY2)(unsafe.Pointer(&dynamic)))

	if checkError("cnc_rddynamic2", ret) {
		// 记录关键动态数据到日志
		logger.Infof("动态数据读取成功: 轴数=%d, 报警状态=%d, 程序号=%d, 进给速度=%d mm/min, 主轴转速=%d rpm",
			dynamic.axis, dynamic.alarm, dynamic.prgnum, dynamic.actf, dynamic.acts)

		// 输出到控制台
		fmt.Printf("  🔄 动态数据汇总:\n")
		fmt.Printf("    - 轴数: %d\n", dynamic.axis)
		fmt.Printf("    - 报警状态: %d\n", dynamic.alarm)
		fmt.Printf("    - 当前程序号: %d\n", dynamic.prgnum)
		fmt.Printf("    - 主程序号: %d\n", dynamic.prgmnum)
		fmt.Printf("    - 当前序列号: %d\n", dynamic.seqnum)
		fmt.Printf("    - 实际进给速度: %d mm/min\n", dynamic.actf)
		fmt.Printf("    - 实际主轴转速: %d rpm\n", dynamic.acts)

		// 使用参考代码的方式访问位置数据
		fmt.Printf("  📍 位置信息 (7轴):\n")
		pos := (*C.ODBDY2_T_POS)(unsafe.Pointer(&dynamic.pos[0]))
		axisNames := []string{"X", "Z", "Y", "A", "C", "CRCR", "CR"}

		// 记录位置数据到日志（调试级别）
		for i := 0; i < 7; i++ {
			logger.Debugf("%s轴位置: 绝对=%d, 机械=%d, 相对=%d, 剩余=%d",
				axisNames[i],
				pos.faxis.absolute[i],
				pos.faxis.machine[i],
				pos.faxis.relative[i],
				pos.faxis.distance[i])

			fmt.Printf("    - %s轴: 绝对=%d, 机械=%d, 相对=%d, 剩余=%d\n",
				axisNames[i],
				pos.faxis.absolute[i],
				pos.faxis.machine[i],
				pos.faxis.relative[i],
				pos.faxis.distance[i])
		}
	} else {
		logger.Error("综合动态数据读取失败")
	}
}

/**
 * 功能3: 程序信息
 *
 * 功能：读取FANUC CNC设备的程序执行状态和程序信息
 *
 * 读取内容：
 * - 当前程序号: 正在执行的程序编号
 * - 当前序列号: 正在执行的程序行号
 * - 执行程序名: 正在执行的程序文件名
 *
 * @param {C.ushort} libh - FOCAS连接句柄
 */
func readProgramInfo(libh C.ushort) {
	printSubTitle("4. 程序信息功能")
	logger.Info("开始读取程序信息")

	// 4.1 读取当前程序号
	logger.Debug("读取当前程序号")
	readCurrentProgramNumber(libh)

	// 4.2 读取当前序列号
	logger.Debug("读取当前序列号")
	readCurrentSequenceNumber(libh)

	// 4.3 读取执行程序名
	logger.Debug("读取执行程序名")
	readExecutingProgramName(libh)

	// 4.4 读取程序内容 (暂时注释)
	//logger.Debug("读取程序内容")
	//readProgramContents(libh)

	logger.Info("程序信息读取完成")
}

/**
 * 4.1 读取当前程序号
 *
 * 功能：读取CNC设备当前正在执行的程序号和主程序号
 *
 * @param {C.ushort} libh - FOCAS连接句柄
 */
func readCurrentProgramNumber(libh C.ushort) {
	fmt.Printf("\n📄 读取当前程序号:\n")
	logger.Debug("开始读取当前程序号")

	var prog C.ODBPRO
	ret := C.cnc_rdprgnum(libh, &prog)

	if checkError("cnc_rdprgnum", ret) {
		logger.Infof("程序号读取成功: 当前程序号=%d, 主程序号=%d", prog.data, prog.mdata)

		fmt.Printf("  📋 程序信息:\n")
		fmt.Printf("    - 当前程序号: %d\n", prog.data)
		fmt.Printf("    - 主程序号: %d\n", prog.mdata)
	} else {
		logger.Error("程序号读取失败")
	}
}

// 4.2 读取当前序列号
func readCurrentSequenceNumber(libh C.ushort) {
	fmt.Printf("\n🔢 读取当前序列号:\n")

	var seq C.ODBSEQ
	ret := C.cnc_rdseqnum(libh, &seq)

	if checkError("cnc_rdseqnum", ret) {
		fmt.Printf("  📊 序列信息:\n")
		fmt.Printf("    - 当前序列号: %d\n", seq.data)
	}
}

// 4.3 读取执行程序名
func readExecutingProgramName(libh C.ushort) {
	fmt.Printf("\n📝 读取执行程序名:\n")

	var exeprog C.ODBEXEPRG
	ret := C.cnc_exeprgname(libh, &exeprog)

	if checkError("cnc_exeprgname", ret) {
		fmt.Printf("  📄 执行程序:\n")
		fmt.Printf("    - 程序名: %s\n", C.GoString(&exeprog.name[0]))
		fmt.Printf("    - 程序号: %d\n", exeprog.o_num)
	}
}

// 4.4 读取程序内容
func readProgramContents(libh C.ushort) {
	fmt.Printf("\n📄 读取程序内容:\n")

	var programContents []byte
	var programSize int = 0
	path := ""
	var _type C.short = 0

	// 转换路径为C字符串
	_path := C.CString(path)
	defer C.free(unsafe.Pointer(_path))

	// 开始上传
	ret := C.cnc_upstart4(libh, _type, _path)
	if !checkError("cnc_upstart4", ret) {
		return
	}

	// 读取程序内容
	for {
		var l C.long = 1280
		buf := make([]byte, l)
		ret := C.cnc_upload4(libh, &l, (*C.char)(unsafe.Pointer(&buf[0])))

		if ret == C.EW_BUFFER {
			continue
		} else if ret != C.EW_OK {
			break
		}

		if l > 0 {
			programSize += int(l)
			programContents = append(programContents, buf[0:l]...)
			if buf[l-1] == '%' {
				break
			}
		} else {
			break
		}
	}

	// 结束上传
	ret = C.cnc_upend4(libh)
	checkError("cnc_upend4", ret)

	if programSize > 0 {
		fmt.Printf("  📋 程序内容 (大小: %d 字节):\n", programSize)
		content := string(programContents)
		if len(content) > 200 {
			fmt.Printf("    - 内容预览: %s...\n", content[:200])
		} else {
			fmt.Printf("    - 完整内容: %s\n", content)
		}
	} else {
		fmt.Printf("  ❌ 无程序内容或读取失败\n")
	}
}

/**
 * 功能4: 状态信息
 *
 * 功能：读取CNC设备的运行状态信息
 *
 * @param {C.ushort} libh - FOCAS连接句柄
 */
func readStatusInfo(libh C.ushort) {
	printSubTitle("5. 状态信息功能")
	logger.Info("开始读取状态信息")
	logger.Warn("状态信息功能开发中，当前为占位符实现")
	fmt.Printf("⏳ 状态信息功能开发中...\n")
	logger.Info("状态信息读取完成（占位符）")
}

// 功能5: 报警信息
func readAlarmInfo(libh C.ushort) {
	printSubTitle("6. 报警信息功能")

	// 6.1 读取报警状态
	readAlarmStatus(libh)

	// 6.2 读取报警消息
	readAlarmMessages(libh)
}

// 6.1 读取报警状态
func readAlarmStatus(libh C.ushort) {
	fmt.Printf("\n🚨 读取报警状态:\n")

	var alarm C.ODBALM
	ret := C.cnc_alarm(libh, &alarm)

	if checkError("cnc_alarm", ret) {
		fmt.Printf("  📊 报警状态: 0x%04x (%d)\n", alarm.data, alarm.data)

		if alarm.data == 0 {
			fmt.Printf("  ✅ 系统正常，无报警\n")
		} else {
			fmt.Printf("  ⚠️  检测到报警，详细信息:\n")
			analyzeAlarmBits(int(alarm.data))
		}
	}
}

// 分析报警位
func analyzeAlarmBits(alarmData int) {
	alarmTypes := map[int]string{
		0:  "参数开关打开 (SW)",
		1:  "断电参数设置 (PW)",
		2:  "I/O错误 (IO)",
		3:  "前台P/S (PS)",
		4:  "超程/外部数据 (OT)",
		5:  "过热报警 (OH)",
		6:  "伺服报警 (SV)",
		7:  "数据I/O错误 (SR)",
		8:  "宏报警 (MC)",
		9:  "主轴报警 (SP)",
		10: "其他报警 (DS)",
		11: "故障预防功能报警 (IE)",
		12: "后台P/S (BG)",
		13: "同步错误 (SN)",
		14: "(保留)",
		15: "外部报警消息 (EX)",
	}

	for bit := 0; bit < 16; bit++ {
		if (alarmData & (1 << bit)) != 0 {
			if desc, exists := alarmTypes[bit]; exists {
				fmt.Printf("    - 位%d: %s\n", bit, desc)
			} else {
				fmt.Printf("    - 位%d: 未知报警类型\n", bit)
			}
		}
	}
}

// 6.2 读取报警消息
func readAlarmMessages(libh C.ushort) {
	fmt.Printf("\n📝 读取报警消息:\n")

	var almmsg C.ODBALMMSG
	var num C.short = 1                            // 读取1条报警消息
	ret := C.cnc_rdalmmsg(libh, -1, &num, &almmsg) // -1表示所有类型

	if checkError("cnc_rdalmmsg", ret) {
		if num == 0 || almmsg.alm_no == 0 {
			fmt.Printf("  ✅ 无活动报警消息\n")
		} else {
			fmt.Printf("  📋 报警详情 (共%d条):\n", num)
			fmt.Printf("    - 报警号: %d\n", almmsg.alm_no)
			fmt.Printf("    - 类型: %d\n", almmsg._type)
			fmt.Printf("    - 轴: %d\n", almmsg.axis)
			fmt.Printf("    - 消息长度: %d\n", almmsg.msg_len)
			if almmsg.msg_len > 0 {
				msg := C.GoString(&almmsg.alm_msg[0])
				fmt.Printf("    - 消息内容: %s\n", msg)
			}
		}
	}
}

/**
 * 功能6: 参数信息
 *
 * 功能：读取FANUC CNC设备的系统参数和配置信息
 *
 * 读取内容：
 * - 常用参数: 工件计数等常用系统参数
 * - 轴相关参数: 各轴的配置参数（暂时注释）
 *
 * @param {C.ushort} libh - FOCAS连接句柄
 */
func readParameterInfo(libh C.ushort) {
	printSubTitle("7. 参数信息功能")
	logger.Info("开始读取参数信息")

	// 7.1 读取常用参数
	logger.Debug("读取常用系统参数")
	readCommonParameters(libh)

	// 7.2 读取轴相关参数 (暂时注释)
	//logger.Debug("读取轴相关参数")
	//readAxisParameters(libh)

	logger.Info("参数信息读取完成")
}

// 7.1 读取常用参数
func readCommonParameters(libh C.ushort) {
	fmt.Printf("\n⚙️  读取常用参数:\n")

	// 读取一些常用的参数号
	commonParams := []struct {
		number int
		name   string
	}{
		{6711, "工件计数"},
		//{3, "绝对/增量设定"},
		//{11, "最大进给速度"},
		//{1851, "最大主轴转速"},
		//{6001, "刀具偏置存储器类型"},
	}

	for _, param := range commonParams {
		readSingleParameter(libh, param.number, param.name)
	}
}

// 7.2 读取轴相关参数
func readAxisParameters(libh C.ushort) {
	fmt.Printf("\n🎯 读取轴相关参数:\n")

	// 读取轴相关参数
	axisParams := []struct {
		number int
		name   string
	}{
		{1020, "快速移动速度"},
		{1421, "参考点位置"},
		{1851, "最大主轴转速"},
	}

	for _, param := range axisParams {
		readAxisParameter(libh, param.number, param.name)
	}
}

/**
 * 读取单个参数
 *
 * 功能：读取指定编号的CNC系统参数
 *
 * @param {C.ushort} libh - FOCAS连接句柄
 * @param {int} number - 参数编号
 * @param {string} name - 参数名称（用于显示）
 */
func readSingleParameter(libh C.ushort, number int, name string) {
	var param C.IODBPSD
	length := C.short(4 + 4) // 4字节头部 + 4字节数据

	ret := C.cnc_rdparam(libh, C.short(number), 0, length, &param)

	if checkError(fmt.Sprintf("cnc_rdparam(%d)", number), ret) {
		// 使用unsafe.Pointer访问联合体中的long数据
		ldata := *(*C.long)(unsafe.Pointer(&param.u[0]))

		logger.Infof("参数读取成功: %s (参数%d) = %d", name, param.datano, ldata)
		fmt.Printf("    - %s (参数%d): %d\n", name, param.datano, ldata)
	} else {
		logger.Errorf("参数读取失败: %s (参数%d)", name, number)
	}
}

// 读取轴参数
func readAxisParameter(libh C.ushort, number int, name string) {
	var param C.IODBPSD
	length := C.short(4 + 4*7) // 4字节头部 + 4字节*7轴

	ret := C.cnc_rdparam(libh, C.short(number), C.ALL_AXES, length, &param)

	if checkError(fmt.Sprintf("cnc_rdparam(%d,ALL_AXES)", number), ret) {
		fmt.Printf("    - %s (参数%d):\n", name, param.datano)
		axisNames := []string{"X", "Z", "Y", "A", "C", "CRCR", "CR"}
		// 使用unsafe.Pointer访问联合体中的long数组
		ldatas := (*[7]C.long)(unsafe.Pointer(&param.u[0]))
		for i := 0; i < 7; i++ {
			fmt.Printf("      %s轴: %d\n", axisNames[i], ldatas[i])
		}
	}
}

// 功能7: 诊断数据
func readDiagnosticData(libh C.ushort) {
	printSubTitle("8. 诊断数据功能")

	// 8.1 读取系统诊断数据
	readSystemDiagnostics(libh)

	// 8.2 读取轴诊断数据
	readAxisDiagnostics(libh)
}

// 8.1 读取系统诊断数据
func readSystemDiagnostics(libh C.ushort) {
	fmt.Printf("\n🔍 读取系统诊断数据:\n")

	// 读取一些常用的诊断数据号
	systemDiags := []struct {
		number int
		name   string
	}{
		{6711, "工件计数"},
		//{1, "报警状态"},
		//{200, "主轴负载"},
		//{300, "进给负载"},
		//{711, "实际进给速度"},
	}

	for _, diag := range systemDiags {
		readSingleDiagnostic(libh, diag.number, diag.name)
	}
}

// 8.2 读取轴诊断数据
func readAxisDiagnostics(libh C.ushort) {
	fmt.Printf("\n📊 读取轴诊断数据:\n")

	// 读取轴相关诊断数据
	axisDiags := []struct {
		number int
		name   string
	}{
		{300, "伺服负载"},
		{301, "伺服电流"},
		{302, "伺服速度"},
	}

	for _, diag := range axisDiags {
		readAxisDiagnostic(libh, diag.number, diag.name)
	}
}

// 读取单个诊断数据
func readSingleDiagnostic(libh C.ushort, number int, name string) {
	var diag C.ODBDGN
	length := C.short(4 + 4) // 4字节头部 + 4字节数据

	ret := C.cnc_diagnoss(libh, C.short(number), 0, length, &diag)

	if checkError(fmt.Sprintf("cnc_diagnoss(%d)", number), ret) {
		// 使用unsafe.Pointer访问联合体中的long数据
		ldata := *(*C.long)(unsafe.Pointer(&diag.u[0]))
		fmt.Printf("    - %s (诊断%d): %d\n", name, diag.datano, ldata)
	}
}

// 读取轴诊断数据
func readAxisDiagnostic(libh C.ushort, number int, name string) {
	var diag C.ODBDGN
	length := C.short(4 + 4*7) // 4字节头部 + 4字节*7轴

	ret := C.cnc_diagnoss(libh, C.short(number), C.ALL_AXES, length, &diag)

	if checkError(fmt.Sprintf("cnc_diagnoss(%d,ALL_AXES)", number), ret) {
		fmt.Printf("    - %s (诊断%d):\n", name, diag.datano)
		axisNames := []string{"X", "Z", "Y", "A", "C", "CRCR", "CR"}
		// 使用unsafe.Pointer访问联合体中的long数组
		ldatas := (*[7]C.long)(unsafe.Pointer(&diag.u[0]))
		for i := 0; i < 7; i++ {
			fmt.Printf("      %s轴: %d\n", axisNames[i], ldatas[i])
		}
	}
}

// 功能8: 刀具信息
func readToolInfo(libh C.ushort) {
	printSubTitle("9. 刀具信息功能")

	// 9.1 读取刀具偏置信息
	readToolOffsetInfo(libh)

	// 9.2 读取刀具偏置数据
	readToolOffsetData(libh)
}

// 9.1 读取刀具偏置信息
func readToolOffsetInfo(libh C.ushort) {
	fmt.Printf("\n🔧 读取刀具偏置信息:\n")

	var tofsinfo C.ODBTLINF
	ret := C.cnc_rdtofsinfo(libh, &tofsinfo)

	if checkError("cnc_rdtofsinfo", ret) {
		fmt.Printf("  📋 刀具偏置配置:\n")
		fmt.Printf("    - 偏置类型: %d\n", tofsinfo.ofs_type)
		fmt.Printf("    - 使用标志: %d\n", tofsinfo.use_no)
	}
}

// 9.2 读取刀具偏置数据
func readToolOffsetData(libh C.ushort) {
	fmt.Printf("\n🛠️  读取刀具偏置数据:\n")

	// 读取前5个刀具的偏置数据
	for toolNum := 1; toolNum <= 5; toolNum++ {
		readSingleToolOffset(libh, toolNum)
	}
}

// 读取单个刀具偏置
func readSingleToolOffset(libh C.ushort, toolNumber int) {
	fmt.Printf("\n  🔨 刀具 %d 偏置:\n", toolNumber)

	// 读取不同类型的偏置
	offsetTypes := []struct {
		typeCode int
		name     string
	}{
		{0, "刀具半径磨损"},
		{1, "刀具半径几何"},
		{2, "刀具长度磨损"},
		{3, "刀具长度几何"},
	}

	for _, offset := range offsetTypes {
		readToolOffsetValue(libh, toolNumber, offset.typeCode, offset.name)
	}
}

// 读取刀具偏置值
func readToolOffsetValue(libh C.ushort, toolNumber int, offsetType int, typeName string) {
	var tofs C.ODBTOFS
	length := C.short(unsafe.Sizeof(tofs))

	ret := C.cnc_rdtofs(libh, C.short(toolNumber), C.short(offsetType), length, &tofs)

	if checkError(fmt.Sprintf("cnc_rdtofs(T%d,type%d)", toolNumber, offsetType), ret) {
		fmt.Printf("    - %s: %d\n", typeName, tofs.data)
	}
}

// 功能9: 负载信息
func readLoadInfo(libh C.ushort) {
	printSubTitle("10. 负载信息功能")

	// 10.1 读取伺服负载计量器数据
	readServoLoadMeter(libh)

	// 10.2 读取主轴负载计量器数据
	readSpindleLoadMeter(libh)

	// 10.3 读取主轴负载信息
	readSpindleLoadInfo(libh)
}

// 10.1 读取伺服负载计量器数据
func readServoLoadMeter(libh C.ushort) {
	fmt.Printf("\n⚡ 读取伺服负载计量器数据:\n")

	var loadmeter [8]C.ODBSVLOAD // 最多8轴
	var data_num C.short = 8

	ret := C.cnc_rdsvmeter(libh, &data_num, (*C.ODBSVLOAD)(unsafe.Pointer(&loadmeter[0])))

	if checkError("cnc_rdsvmeter", ret) {
		fmt.Printf("  📊 伺服负载数据 (共%d轴):\n", data_num)
		for i := 0; i < int(data_num); i++ {
			load := &loadmeter[i]
			axisName := C.GoString(&load.svload.name)
			loadValue := int32(load.svload.data)
			decimalPlace := int16(load.svload.dec)
			unit := int16(load.svload.unit)

			// 根据小数位数计算实际值
			actualValue := float64(loadValue)
			if decimalPlace > 0 {
				for j := 0; j < int(decimalPlace); j++ {
					actualValue /= 10.0
				}
			}

			unitStr := "%"
			if unit == 1 {
				unitStr = "rpm"
			}

			fmt.Printf("    - %s轴: %.1f%s (原始值: %d, 小数位: %d)\n",
				axisName, actualValue, unitStr, loadValue, decimalPlace)
		}
	}
}

// 10.2 读取主轴负载计量器数据
func readSpindleLoadMeter(libh C.ushort) {
	fmt.Printf("\n🔄 读取主轴负载计量器数据:\n")

	// 读取负载数据 (type = 0)
	readSpindleLoadMeterByType(libh, 0, "负载")

	// 读取转速数据 (type = 1)
	readSpindleLoadMeterByType(libh, 1, "转速")
}

// 按类型读取主轴负载计量器数据
func readSpindleLoadMeterByType(libh C.ushort, dataType int, typeName string) {
	var loadmeter [4]C.ODBSPLOAD // 最多4个主轴
	var data_num C.short = 4

	ret := C.cnc_rdspmeter(libh, C.short(dataType), &data_num, (*C.ODBSPLOAD)(unsafe.Pointer(&loadmeter[0])))

	if checkError(fmt.Sprintf("cnc_rdspmeter(type=%d)", dataType), ret) {
		fmt.Printf("  🎯 主轴%s数据 (共%d个主轴):\n", typeName, data_num)
		for i := 0; i < int(data_num); i++ {
			load := &loadmeter[i]

			// 根据类型选择数据源
			var dataSource *C.LOADELM
			if dataType == 0 {
				dataSource = &load.spload // 负载数据
			} else {
				dataSource = &load.spspeed // 转速数据
			}

			spindleName := C.GoString(&dataSource.name)
			suff1 := C.GoString(&dataSource.suff1)
			loadValue := int32(dataSource.data)
			decimalPlace := int16(dataSource.dec)
			unit := int16(dataSource.unit)

			// 根据小数位数计算实际值
			actualValue := float64(loadValue)
			if decimalPlace > 0 {
				for j := 0; j < int(decimalPlace); j++ {
					actualValue /= 10.0
				}
			}

			unitStr := "%"
			if unit == 1 {
				unitStr = "rpm"
			}

			fmt.Printf("    - %s%s: %.1f%s (原始值: %d, 小数位: %d)\n",
				spindleName, suff1, actualValue, unitStr, loadValue, decimalPlace)
		}
	}
}

// 10.3 读取主轴负载信息
func readSpindleLoadInfo(libh C.ushort) {
	fmt.Printf("\n🔧 读取主轴负载信息:\n")

	// 读取所有主轴的负载信息
	var spload C.ODBSPN
	ret := C.cnc_rdspload(libh, C.ALL_SPINDLES, &spload)

	if checkError("cnc_rdspload(ALL_SPINDLES)", ret) {
		fmt.Printf("  📈 主轴负载信息:\n")
		fmt.Printf("    - 主轴号: %d\n", spload.datano)
		fmt.Printf("    - 类型: %d\n", spload._type)

		// 显示各主轴负载数据
		for i := 0; i < 4; i++ { // 最多4个主轴
			if spload.data[i] != 0 {
				fmt.Printf("    - 主轴%d负载: %d\n", i+1, spload.data[i])
			}
		}
	}

	// 读取单个主轴的负载信息
	for spindleNum := 1; spindleNum <= 2; spindleNum++ {
		readSingleSpindleLoad(libh, spindleNum)
	}
}

// 读取单个主轴负载
func readSingleSpindleLoad(libh C.ushort, spindleNum int) {
	var spload C.ODBSPN
	ret := C.cnc_rdspload(libh, C.short(spindleNum), &spload)

	if checkError(fmt.Sprintf("cnc_rdspload(S%d)", spindleNum), ret) {
		fmt.Printf("    - 主轴%d详细信息: 负载=%d\n", spindleNum, spload.data[0])
	}
}
