/docker-service/mongodb_data
/docker-service/redis_data
/docker-service/influxdb_data
/docker-service/nats_data
/docker-service/sqlite_data
02_device_collect/device_collect
*.pid
*.log
*.db
/.idea
/11_server_jet
02_device_collect/device-collect
02_generate_data/generator
/test_logs
/demo_logs
/01_device_api/fanuc/test
/01_device_api/fanuc/test
/01_device_api/fanuc_v2/focas2_doc
01_device_api/fanuc_v2/main
01_device_api/fanuc_v2.zip
01_device_api/fanuc_v2/build/fanuc-collector-v2-safe
/01_device_api/fanuc_v2/build
01_device_api/fanuc_v2/test_device_connection
*old*
*backup*
*.bak
/31_machine_backend/app/__pycache__
/31_machine_backend/app/data/__pycache__
*.pyc
/30_machine_status/30_machine_status.git
12_server_api/server-api
/react_app
*test*
/_03_data_cleaning
/_04_data_push
/_31_machine_backend
/01_device_api/_fanuc
/12_server_api/build
/docker-service/_mongodb_data
02_generate_data/generate_data
02_generate_data/data/production_data.json
*.gz
/node_modules
package-lock.json
package.json
package-lock.json
05_data_push/data_push
12_server_api/server_api
12_server_api/server_api_new
*debug*
/12_server_api/handlers/_bak
/docker-service
fix_*
/01_device_api/fanuc_v2/logs
31_machine_status_without_nextjs
31_machine_status_react_only
/01_device_api/fanuc_v2/bin
01_device_api/fanuc_v2/single_collect
01_device_api/fanuc_v3/example/01.self-execute/self-process
01_device_api/fanuc_v3/example/01.self-execute/process/self-process
01_device_api/fanuc_v3/example/01.self-execute/self-manage
01_device_api/fanuc_v3/example/01.self-execute/self-manage_linux
01_device_api/fanuc_v3/example/01.self-execute/self-process_linux
01_device_api/fanuc_v3/example/02.self-execute_api/self-manage
01_device_api/fanuc_v3/example/02.self-execute_api/self-process
01_device_api/fanuc_v3/example/01.self-execute/manage/self-execute
01_device_api/fanuc_v3/example/01.self-execute/manage/self-process
